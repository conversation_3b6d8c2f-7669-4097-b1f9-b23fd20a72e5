#!/usr/bin/env python3
"""
Dynamic MCP Server Factory
Creates and manages dynamic MCP servers for various services
"""
import asyncio
import json
import logging
import os
import subprocess
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import tempfile
import shutil
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DynamicMCPFactory")

class DynamicMCPFactory:
    """Factory for creating and managing dynamic MCP servers"""
    
    def __init__(self):
        self.servers: Dict[str, Dict] = {}
        self.processes: Dict[str, subprocess.Popen] = {}
        self.server_dir = Path("dynamic_servers")
        self.server_dir.mkdir(exist_ok=True)
        logger.info("Dynamic MCP Factory initialized")
    
    def _generate_server_id(self) -> str:
        """Generate unique server ID"""
        return f"mcp_{uuid.uuid4().hex[:8]}"
    
    async def create_server(self, server_type: str, category: str, service: str, credentials: Dict[str, Any],
                          server_name: str, description: str = "", user_id: Optional[str] = None) -> Dict:
            
        """Create a new dynamic MCP server"""
        try:
            # Generate user_id if not provided or if it's the default "string"
            if not user_id or user_id == "string":
                user_id = f"user_{uuid.uuid4().hex[:8]}"
                logger.info(f"Generated user_id: {user_id} for server creation")
            else:
                logger.info(f"Using provided user_id: {user_id} for server creation")
            
            # Validate server type and category
            valid_categories = {
                "custom": ["cloud_storage", "databases", "devops", "git"],
                "public": ["locally_available"]
            }
            valid_services = {
                "cloud_storage": ["aws_s3", "gcs_drive", "google_drive" "microsoft_sharepoint", "azure_blob", "onedrive"],
                "databases": ["postgres", "mysql", "mongodb", "redis", "sql_server"],
                "devops": ["jira", "azure_devops"],
                "git": ["github", "bitbucket", "azure_repos"],
                "locally_available": ["airbnb", "news", "google", "weather", "brave_search", "google_maps"]
            }
            
            if server_type not in valid_categories:
                raise ValueError(f"Invalid server type: {server_type}")
            if category not in valid_categories[server_type]:
                raise ValueError(f"Invalid category {category} for server type {server_type}")
            if service not in valid_services[category]:
                raise ValueError(f"Invalid service {service} for category {category}")
            
            server_id = self._generate_server_id()
            
            # Create server directory
            server_path = self.server_dir / server_id
            server_path.mkdir(exist_ok=True)
            
            # Generate server configuration
            config = {
                "server_id": server_id,
                "server_name": server_name,
                "server_type": server_type,
                "category": category,
                "service": service,
                "credentials": credentials,
                "description": description,
                "status": "created",
                "createdAt": datetime.now(),
                "last_used": None,
                "running": False,
                "user_id": user_id
            }
            
            # Generate server code based on service type
            server_code = self._generate_server_code(config)
            
            # Write server file
            server_file = server_path / f"{server_id}.py"
            with open(server_file, 'w') as f:
                f.write(server_code)
            
            # Store server script path
            config["script_path"] = str(server_file)
            
            # Store server info
            self.servers[server_id] = config
            
            logger.info(f"Created dynamic MCP server: {server_id} for {service} with category {category} for user {user_id}")
            
            # Return response with user_id and script_path
            return {
                "success": True,
                "server_id": server_id,
                "user_id": user_id,
                "server_info": config,
                "script_path": str(server_file),  # Include script_path in response
                "message": f"Successfully created {server_type} MCP server for {service} with dynamic agent"
            }
            
        except Exception as e:
            logger.error(f"Error creating MCP server: {e}")
            raise
    
    def _generate_server_code(self, config: Dict) -> str:
        """Generate MCP server code based on service type"""
        service = config["service"]
        if service == "aws_s3":
            return self._generate_aws_s3_server(config)
        elif service == "gcs_drive":
            return self._generate_gcs_server(config)
        elif service == "google_drive":
            return self._generate_google_drive_server(config)
        elif service == "microsoft_sharepoint":
            return self._generate_sharepoint_server(config)
        elif service == "onedrive":
            return self._generate_onedrive_server(config)
        elif service == "azure_blob":
            return self._generate_azure_blob_server(config)
        elif service in ["postgres", "mysql", "mongodb", "redis", "sql_server"]:
            return self._generate_database_server(config)
        elif service in ["jira", "azure_devops"]:
            return self._generate_devops_server(config)
        elif service in ["github", "bitbucket", "azure_repos"]:
            return self._generate_git_server(config)
        elif service in ["airbnb", "weather", "news", "google", "brave_search", "google_maps"]:
            return self._generate_public_server(config)
        else:
            raise ValueError(f"Unsupported service: {service}")
    
    def _generate_aws_s3_server(self, config: Dict) -> str:

        '''Generate AWS S3 MCP server code with Gemini LLM for image and PDF text extraction'''
        credentials = config["credentials"]
        server_id = config["server_id"]
    
        return f'''#!/usr/bin/env python3
"""
Dynamic AWS S3 MCP Server - {server_id}
"""
import logging
import boto3
import requests
from mcp.server.fastmcp import FastMCP
from botocore.exceptions import ClientError, NoCredentialsError
from io import BytesIO
from PIL import Image
import base64
import json
import re
from pdf2image import convert_from_bytes

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AWS_S3_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("aws_s3_{server_id}", redirect_slashes=False, stateless_http=True)

# AWS S3 Configuration
AWS_ACCESS_KEY_ID = "{credentials.get('aws_access_key_id', '')}"
AWS_SECRET_ACCESS_KEY = "{credentials.get('aws_secret_access_key', '')}"
BUCKET_NAME = "{credentials.get('bucket_name', '')}"
REGION = "{credentials.get('region', 'us-east-1')}"
SESSION_TOKEN = "{credentials.get('session_token', '')}"

# Gemini API Configuration
GEMINI_API_KEY = "{credentials.get('gemini_api_key', '')}"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

# Initialize S3 client
try:
    session_kwargs = {{
        'aws_access_key_id': AWS_ACCESS_KEY_ID,
        'aws_secret_access_key': AWS_SECRET_ACCESS_KEY,
        'region_name': REGION
    }}
    if SESSION_TOKEN:
        session_kwargs['aws_session_token'] = SESSION_TOKEN
    
    s3_client = boto3.client('s3', **session_kwargs)
    logger.info("AWS S3 client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize S3 client: {{e}}")
    s3_client = None

@mcp.tool()
def list_s3_objects(prefix: str = "") -> str:
    """List objects in S3 bucket"""
    try:
        if not s3_client:
            return "S3 client not initialized"
        
        response = s3_client.list_objects_v2(
            Bucket=BUCKET_NAME,
            Prefix=prefix,
            MaxKeys=100
        )
        
        objects = response.get('Contents', [])
        if not objects:
            return f"No objects found in bucket {{BUCKET_NAME}} with prefix '{{prefix}}'"
        
        result = f"Objects in {{BUCKET_NAME}}:\\n"
        for obj in objects:
            result += f"- {{obj['Key']}} ({{obj['Size']}} bytes, {{obj['LastModified']}})\\n"
        
        return result
    except Exception as e:
        return f"Error listing S3 objects: {{str(e)}}"

@mcp.tool()
def get_s3_object_content(key: str) -> str:
    """Get content of S3 object"""
    try:
        if not s3_client:
            return "S3 client not initialized"
        
        response = s3_client.get_object(Bucket=BUCKET_NAME, Key=key)
        content = response['Body'].read().decode('utf-8', errors='ignore')
        return f"Content of {{key}}:\\n{{content}}"
    except UnicodeDecodeError:
        return f"Content of {{key}} is binary (possibly an image or PDF). Use extract_image_text or extract_pdf_text to process."
    except Exception as e:
        return f"Error getting S3 object content: {{str(e)}}"

@mcp.tool()
def search_s3_objects(search_term: str) -> str:
    """Search for objects in S3 bucket by name"""
    try:
        if not s3_client:
            return "S3 client not initialized"
        
        response = s3_client.list_objects_v2(Bucket=BUCKET_NAME)
        objects = response.get('Contents', [])
        
        matching_objects = [obj for obj in objects if search_term.lower() in obj['Key'].lower()]
        
        if not matching_objects:
            return f"No objects found matching '{{search_term}}'"
        
        result = f"Objects matching '{{search_term}}':\\n"
        for obj in matching_objects:
            result += f"- {{obj['Key']}} ({{obj['Size']}} bytes)\\n"
        
        return result
    except Exception as e:
        return f"Error searching S3 objects: {{str(e)}}"

@mcp.tool()
def extract_image_text(key: str, query: str = "Extract all text from the image") -> str:
    """Extract text from an image in S3 using Gemini LLM"""
    try:
        if not s3_client:
            return "S3 client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for image text extraction"

        # Check if the file is an image
        if not any(key.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.bmp']):
            return f"File {{key}} is not a supported image format (PNG, JPG, JPEG, BMP)"

        # Download image from S3
        response = s3_client.get_object(Bucket=BUCKET_NAME, Key=key)
        image_data = response['Body'].read()

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare Gemini API request
        headers = {{
            'Content-Type': 'application/json'
        }}
        payload = {{
            "contents": [{{
                "parts": [
                    {{"text": query}},
                    {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                ]
            }}]
        }}

        # Call Gemini API
        response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers, json=payload)
        response.raise_for_status()

        result = response.json()
        extracted_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', 'No text extracted')

        return f"Extracted text from {{key}}:\\n{{extracted_text}}\\n\\nSource: s3://{{BUCKET_NAME}}/{{key}}"
    except Exception as e:
        return f"Error extracting text from image {{key}}: {{str(e)}}"

@mcp.tool()
def extract_pdf_text(key: str, query: str = "Extract all text from the PDF") -> str:
    """Extract text from a PDF in S3 using Gemini LLM"""
    try:
        if not s3_client:
            return "S3 client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for PDF text extraction"

        # Check if the file is a PDF
        if not key.lower().endswith('.pdf'):
            return f"File {{key}} is not a PDF"

        # Download PDF from S3
        response = s3_client.get_object(Bucket=BUCKET_NAME, Key=key)
        pdf_data = response['Body'].read()

        # Convert PDF to images
        images = convert_from_bytes(pdf_data)
        extracted_texts = []

        for i, image in enumerate(images):
            # Convert image to base64
            buffer = BytesIO()
            image.save(buffer, format="JPEG")
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Prepare Gemini API request
            headers = {{
                'Content-Type': 'application/json'
            }}
            payload = {{
                "contents": [{{
                    "parts": [
                        {{"text": f"{{query}} (Page {{i+1}})"}},
                        {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                    ]
                }}]
            }}

            # Call Gemini API
            response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers, json=payload)
            response.raise_for_status()

            result = response.json()
            page_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', f"No text extracted from page {{i+1}}")
            extracted_texts.append(f"Page {{i+1}}:\\n{{page_text}}\\n")

        result_str = f"Extracted text from {{key}}:\\n\\n{{'\\n'.join(extracted_texts)}}\\nSource: s3://{{BUCKET_NAME}}/{{key}}"
        return result_str
    except Exception as e:
        return f"Error extracting text from PDF {{key}}: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting AWS S3 MCP server with stdio transport")
    mcp.run(transport="stdio")
'''
    
    def _generate_gcs_server(self, config: Dict) -> str:
        """Generate Google Cloud Storage MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]
        
        return f'''#!/usr/bin/env python3
"""
Dynamic Google Cloud Storage MCP Server - {server_id}
"""
import logging
import json
from google.cloud import storage
from google.oauth2 import service_account
from mcp.server.fastmcp import FastMCP
from io import BytesIO
from PIL import Image
import base64
import re
import requests
from pdf2image import convert_from_bytes
from time import sleep

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GCS_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("gcs_{server_id}", redirect_slashes=False, stateless_http=True)

# GCS Configuration
SERVICE_ACCOUNT_JSON ="{credentials.get('service_account_key_json', '')}
PROJECT_ID = "{credentials.get('project_id', '')}"
BUCKET_NAME = "{credentials.get('bucket_name', '')}"
CREDENTIALS = {json.dumps(credentials.get('gcs_credentials', {}))}
GEMINI_API_KEY = "{credentials.get('gemini_api_key', '')}"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

# Initialize GCS client
try:
    if SERVICE_ACCOUNT_JSON:
        service_account_info = json.loads(SERVICE_ACCOUNT_JSON)
        credentials_obj = service_account.Credentials.from_service_account_info(service_account_info)
        gcs_client = storage.Client(credentials=credentials_obj, project=PROJECT_ID)
    else:
        gcs_client = storage.Client(project=PROJECT_ID)
    
    bucket = gcs_client.bucket(BUCKET_NAME)
    logger.info("GCS client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize GCS client: {{e}}")
    gcs_client = None
    bucket = None

@mcp.tool()
def list_gcs_objects(prefix: str = "") -> str:
    """List objects in GCS bucket"""
    try:
        if not bucket:
            return "GCS client not initialized"
        
        blobs = list(bucket.list_blobs(prefix=prefix, max_results=100))
        
        if not blobs:
            return f"No objects found in bucket {{BUCKET_NAME}} with prefix '{{prefix}}'"
        
        result = f"Objects in {{BUCKET_NAME}}:\\n"
        for blob in blobs:
            result += f"- {{blob.name}} ({{blob.size}} bytes, {{blob.time_created}})\\n"
        
        return result
    except Exception as e:
        return f"Error listing GCS objects: {{str(e)}}"

@mcp.tool()
def get_gcs_object_content(object_name: str) -> str:
    """Get content of GCS object"""
    try:
        if not bucket:
            return "GCS client not initialized"
        
        blob = bucket.blob(object_name)
        content = blob.download_as_text()
        return f"Content of {{object_name}}:\\n{{content}}"
    except Exception as e:
        return f"Error getting GCS object content: {{str(e)}}"

@mcp.tool()
def search_gcs_objects(search_term: str) -> str:
    """Search for objects in GCS bucket by name"""
    try:
        if not bucket:
            return "GCS client not initialized"
        
        blobs = list(bucket.list_blobs())
        matching_blobs = [blob for blob in blobs if search_term.lower() in blob.name.lower()]
        
        if not matching_blobs:
            return f"No objects found matching '{{search_term}}'"
        
        result = f"Objects matching '{{search_term}}':\\n"
        for blob in matching_blobs:
            result += f"- {{blob.name}} ({{blob.size}} bytes)\\n"
        
        return result
    except Exception as e:
        return f"Error searching GCS objects: {{str(e)}}"
    
@mcp.tool()
def extract_image_text(key: str, query: str = "Extract all text from the image") -> str:
    """Extract text from an image in GCS using Gemini LLM"""
    try:
        if not bucket:
            return "GCS client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for image text extraction"

        # Check if the file is an image
        if not any(key.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.bmp']):
            return f"File {{key}} is not a supported image format (PNG, JPG, JPEG, BMP)"

        # Download image
        blob = bucket.blob(key)
        image_data = blob.download_as_bytes()

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare Gemini API request
        headers = {{
            'Content-Type': 'application/json'
        }}
        payload = {{
            "contents": [{{
                "parts": [
                    {{"text": query}},
                    {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                ]
            }}]
        }}

        # Call Gemini API with retry logic
        retries = 3
        for attempt in range(retries):
            try:
                response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers, json=payload)
                response.raise_for_status()
                break
            except requests.exceptions.HTTPError as e:
                if response.status_code == 429 and attempt < retries - 1:
                    sleep(2 ** attempt)
                    continue
                raise

        result = response.json()
        extracted_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', 'No text extracted')

        return f"Extracted text from {{key}}:\\n{{extracted_text}}\\n\\nSource: gs://{{BUCKET_NAME}}/{{key}}"
    except Exception as e:
        return f"Error extracting text from image {{key}}: {{str(e)}}"

@mcp.tool()
def extract_pdf_text(key: str, query: str = "Extract all text from the PDF") -> str:
    """Extract text from a PDF in GCS using Gemini LLM"""
    try:
        if not bucket:
            return "GCS client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for PDF text extraction"

        # Check if the file is a PDF
        if not key.lower().endswith('.pdf'):
            return f"File {{key}} is not a PDF"

        # Download PDF
        blob = bucket.blob(key)
        pdf_data = blob.download_as_bytes()

        # Convert PDF to images
        images = convert_from_bytes(pdf_data, first_page=1, last_page=10)  # Limit to first 10 pages
        extracted_texts = []

        for i, image in enumerate(images):
            # Resize image to reduce payload size
            image.thumbnail((1024, 1024))
            # Convert image to base64
            buffer = BytesIO()
            image.save(buffer, format="JPEG")
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Prepare Gemini API request
            headers = {{
                'Content-Type': 'application/json'
            }}
            payload = {{
                "contents": [{{
                    "parts": [
                        {{"text": f"{{query}} (Page {{i+1}})"}},
                        {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                    ]
                }}]
            }}

            # Call Gemini API with retry logic
            retries = 3
            for attempt in range(retries):
                try:
                    response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers, json=payload)
                    response.raise_for_status()
                    break
                except requests.exceptions.HTTPError as e:
                    if response.status_code == 429 and attempt < retries - 1:
                        sleep(2 ** attempt)
                        continue
                    raise

            result = response.json()
            page_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', f"No text extracted from page {{i+1}}")
            extracted_texts.append(f"Page {{i+1}}:\\n{{page_text}}\\n")

        result_str = f"Extracted text from {{key}}:\\n\\n{{'\\n'.join(extracted_texts)}}\\nSource: gs://{{BUCKET_NAME}}/{{key}}"
        return result_str
    except Exception as e:
        return f"Error extracting text from PDF {{key}}: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting GCS MCP server with stdio transport")
    mcp.run(transport="stdio")
'''
    
    def _generate_google_drive_server(self, config: Dict) -> str:
        """Generate Google Drive MCP server code for URL-based access and with Gemini LLM for image and PDF text extraction"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic Google Drive MCP Server - {server_id}
URL-based access to Google Drive files
with Gemini LLM for image and PDF text extraction
"""
import logging
import requests
import re
from mcp.server.fastmcp import FastMCP
from urllib.parse import urlparse, parse_qs
from io import BytesIO
from PIL import Image
import base64
import json
import re
from pdf2image import convert_from_bytes
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaIoBaseDownload

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GoogleDrive_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("google_drive_{server_id}", redirect_slashes=False, stateless_http=True)

# Google Drive URL from credentials
CREDENTIALS = {json.dumps(credentials.get('google_credentials', {}))}
DRIVE_URL = "{credentials.get('url', '')}"
GEMINI_API_KEY = "{credentials.get('gemini_api_key', '')}"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

# Initialize Google Drive client
try:
    credentials = service_account.Credentials.from_service_account_info(CREDENTIALS)
    drive_service = build('drive', 'v3', credentials=credentials)
    logger.info("Google Drive client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Google Drive client: {{e}}")
    drive_service = None

def extract_file_id_from_url(url: str) -> str:
    """Extract Google Drive file ID from various URL formats"""
    try:
        # Handle different Google Drive URL formats
        patterns = [
            r'/file/d/([a-zA-Z0-9-_]+)',  # /file/d/FILE_ID/view
            r'id=([a-zA-Z0-9-_]+)',       # ?id=FILE_ID
            r'/d/([a-zA-Z0-9-_]+)',       # /d/FILE_ID
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return None
    except Exception as e:
        logger.error(f"Error extracting file ID: {{e}}")
        return None

def get_file_content_from_url(url: str) -> str:
    """Get file content from Google Drive URL"""
    try:
        file_id = extract_file_id_from_url(url)
        if not file_id:
            return f"Could not extract file ID from URL: {{url}}"

        # Try to get file content using public access
        export_url = f"https://drive.google.com/uc?id={{file_id}}&export=download"

        headers = {{
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }}

        response = requests.get(export_url, headers=headers, timeout=30)

        if response.status_code == 200:
            # Try to decode as text
            try:
                content = response.text
                return content[:5000]  # Limit to first 5000 characters
            except:
                return f"File downloaded but content is binary ({{len(response.content)}} bytes)"
        else:
            return f"Could not access file. Status: {{response.status_code}}. Make sure the file is publicly accessible."

    except Exception as e:
        return f"Error accessing Google Drive file: {{str(e)}}"

@mcp.tool()
def query_drive_file(query: str) -> str:
    """Query the configured Google Drive file

    Args:
        query: Question or search query about the file
    """
    try:
        if not DRIVE_URL:
            return "No Google Drive URL configured. Please provide a URL in the credentials when creating the server."

        # Get file content from the configured URL
        content = get_file_content_from_url(DRIVE_URL)

        if "Error" in content or "Could not" in content:
            return content

        # Simple query processing
        query_lower = query.lower()
        content_lower = content.lower()

        # Extract relevant parts based on query
        if any(word in query_lower for word in ['summary', 'summarize', 'overview']):
            # Return first few paragraphs as summary
            paragraphs = content.split('\\n\\n')[:3]
            result = "**Summary of Google Drive file:**\\n\\n"
            result += '\\n\\n'.join(paragraphs)
        elif any(word in query_lower for word in ['search', 'find', 'contains']):
            # Search for specific terms
            search_terms = [word for word in query.split() if len(word) > 3]
            found_lines = []
            for line in content.split('\\n'):
                if any(term.lower() in line.lower() for term in search_terms):
                    found_lines.append(line.strip())

            if found_lines:
                result = f"**Found {{len(found_lines)}} relevant lines:**\\n\\n"
                result += '\\n'.join(found_lines[:10])  # Limit to 10 lines
            else:
                result = f"No content found matching your search terms: {{', '.join(search_terms)}}"
        else:
            # General query - return content with context
            result = f"**Content from Google Drive file:**\\n\\n"
            result += content[:2000]  # Limit response length
            if len(content) > 2000:
                result += "\\n\\n... (content truncated)"

        result += f"\\n\\n**Source:** {{DRIVE_URL}}"
        return result

    except Exception as e:
        return f"Error querying Google Drive file: {{str(e)}}"

@mcp.tool()
def get_file_info() -> str:
    """Get information about the configured Google Drive file"""
    try:
        if not DRIVE_URL:
            return "No Google Drive URL configured. Please provide a URL in the credentials when creating the server."

        file_id = extract_file_id_from_url(DRIVE_URL)
        if not file_id:
            return f"Could not extract file ID from URL: {{DRIVE_URL}}"

        result = f"**Google Drive File Information:**\\n\\n"
        result += f"🔗 **URL:** {{DRIVE_URL}}\\n"
        result += f"🆔 **File ID:** {{file_id}}\\n"

        # Try to get basic file info
        content = get_file_content_from_url(DRIVE_URL)

        if "Error" in content or "Could not" in content:
            result += f"⚠️ **Status:** {{content}}\\n"
        else:
            content_length = len(content)
            result += f"✅ **Status:** Accessible\\n"
            result += f"📄 **Content Length:** {{content_length}} characters\\n"
            result += f"📝 **Preview:** {{content[:200]}}...\\n"

        result += f"\\n💡 **Usage:** Use query_drive_file() to search this file"
        return result

    except Exception as e:
        return f"Error getting file info: {{str(e)}}"

@mcp.tool()
def extract_image_text(file_id: str, query: str = "Extract all text from the image") -> str:
    """Extract text from an image in Google Drive using Gemini LLM"""
    try:
        if not drive_service:
            return "Google Drive client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for image text extraction"

        # Get file metadata to check MIME type
        file_metadata = drive_service.files().get(file_id=file_id).execute()
        mime_type = file_metadata.get('mimeType', '')
        if not any(mime_type.endswith(ext) for ext in ['image/png', 'image/jpeg', 'image/bmp']):
            return f"File {{file_metadata['name']}} is not a supported image format (PNG, JPG, JPEG, BMP)"

        # Download image
        request = drive_service.files().get_media(fileId=file_id)
        file_content = BytesIO()
        downloader = MediaIoBaseDownload(file_content, request)
        done = False
        while not done:
            status, done = downloader.next_chunk()
        
        # Convert image to base64
        image_data = file_content.getvalue()
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare Gemini API request
        headers = {{
            'Content-Type': 'application/json'
        }}
        payload = {{
            "contents": [{{
                "parts": [
                    {{"text": query}},
                    {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                ]
            }}]
        }}

        # Call Gemini API with retry logic
        retries = 3
        for attempt in range(retries):
            try:
                response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers, json=payload)
                response.raise_for_status()
                break
            except requests.exceptions.HTTPError as e:
                if response.status_code == 429 and attempt < retries - 1:
                    sleep(2 ** attempt)
                    continue
                raise

        result = response.json()
        extracted_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', 'No text extracted')

        return f"Extracted text from {{file_metadata['name']}} (ID: {{file_id}}):\\n{{extracted_text}}\\n\\nSource: https://drive.google.com/file/d/{{file_id}}"
    except Exception as e:
        return f"Error extracting text from image {{file_metadata.get('name', file_id)}}: {{str(e)}}"

@mcp.tool()
def extract_pdf_text(file_id: str, query: str = "Extract all text from the PDF") -> str:
    """Extract text from a PDF in Google Drive using Gemini LLM"""
    try:
        if not drive_service:
            return "Google Drive client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for PDF text extraction"

        # Get file metadata to check MIME type
        file_metadata = drive_service.files().get(file_id=file_id).execute()
        if file_metadata.get('mimeType') != 'application/pdf':
            return f"File {{file_metadata['name']}} is not a PDF"

        # Download PDF
        request = drive_service.files().get_media(fileId=file_id)
        file_content = BytesIO()
        downloader = MediaIoBaseDownload(file_content, request)
        done = False
        while not done:
            status, done = downloader.next_chunk()
        
        # Convert PDF to images
        pdf_data = file_content.getvalue()
        images = convert_from_bytes(pdf_data, first_page=1, last_page=10)  # Limit to first 10 pages
        extracted_texts = []

        for i, image in enumerate(images):
            # Resize image to reduce payload size
            image.thumbnail((1024, 1024))
            # Convert image to base64
            buffer = BytesIO()
            image.save(buffer, format="JPEG")
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Prepare Gemini API request
            headers = {{
                'Content-Type': 'application/json'
            }}
            payload = {{
                "contents": [{{
                    "parts": [
                        {{"text": f"{{query}} (Page {{i+1}})"}},
                        {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                    ]
                }}]
            }}

            # Call Gemini API with retry logic
            retries = 3
            for attempt in range(retries):
                try:
                    response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers, json=payload)
                    response.raise_for_status()
                    break
                except requests.exceptions.HTTPError as e:
                    if response.status_code == 429 and attempt < retries - 1:
                        sleep(2 ** attempt)
                        continue
                    raise

            result = response.json()
            page_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', f"No text extracted from page {{i+1}}")
            extracted_texts.append(f"Page {{i+1}}:\\n{{page_text}}\\n")

        result_str = f"Extracted text from {{file_metadata['name']}} (ID: {{file_id}}):\\n\\n{{'\\n'.join(extracted_texts)}}\\nSource: https://drive.google.com/file/d/{{file_id}}"
        return result_str
    except Exception as e:
        return f"Error extracting text from PDF {{file_metadata.get('name', file_id)}}: {{str(e)}}"


if __name__ == "__main__":
    logger.info("Starting Google Drive URL-based MCP server with stdio transport")
    mcp.run(transport="stdio")
'''
    
    def _generate_sharepoint_server(self, config: Dict) -> str:
        """Generate Microsoft SharePoint MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic Microsoft SharePoint MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP
from io import BytesIO
from PIL import Image
import base64
import json
import re
from pdf2image import convert_from_bytes
from time import sleep

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("SharePoint_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("sharepoint_{server_id}", redirect_slashes=False, stateless_http=True)

# SharePoint Configuration
TENANT_ID = "{credentials.get('tenant_id', '')}"
CLIENT_ID = "{credentials.get('client_id', '')}"
CLIENT_SECRET = "{credentials.get('client_secret', '')}"
SITE_ID = "{credentials.get('site_id', '')}"
SHAREPOINT_SITE_URL = "{credentials.get('sharepoint_site_url', '')}"
RESOURCE_DRIVE_ID = "{credentials.get('resource_drive_id', '')}"
GEMINI_API_KEY = "{credentials.get('gemini_api_key', '')}"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

# Initialize SharePoint client (Microsoft Graph API)
try:
    authority = f"https://login.microsoftonline.com/{{TENANT_ID}}"
    app = ConfidentialClientApplication(
        CLIENT_ID,
        authority=authority,
        client_credential=CLIENT_SECRET
    )
    token_response = app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
    access_token = token_response.get('access_token')
    if not access_token:
        raise Exception("Failed to acquire access token")
    headers = {{
        'Authorization': f'Bearer {{access_token}}',
        'Content-Type': 'application/json'
    }}
    logger.info("SharePoint client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize SharePoint client: {{e}}")
    headers = None

@mcp.tool()
def list_sharepoint_files(folder_path: str = "") -> str:
    """List files in SharePoint site"""
    try:
        if not headers:
            return "SharePoint client not initialized"
        
        url = f"https://graph.microsoft.com/v1.0/sites/{{SITE_ID}}/drive/root:/{{folder_path}}:/children"
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        files = response.json().get('value', [])
        
        if not files:
            return f"No files found in SharePoint site {{SITE_ID}} at path '{{folder_path}}'"
        
        result = f"Files in SharePoint site {{SITE_ID}}:\\n"
        for file in files:
            result += f"- {{file['name']}} (ID: {{file['id']}}, {{file['size']}} bytes, {{file['lastModifiedDateTime']}})\\n"
        
        return result
    except Exception as e:
        return f"Error listing SharePoint files: {{str(e)}}"

@mcp.tool()
def get_sharepoint_file_content(file_id: str) -> str:
    """Get content of a SharePoint file"""
    try:
        if not headers:
            return "SharePoint client not initialized"
        
        url = f"https://graph.microsoft.com/v1.0/sites/{{SITE_ID}}/drive/items/{{file_id}}"
        metadata_response = requests.get(url, headers=headers)
        metadata_response.raise_for_status()
        file_metadata = metadata_response.json()
        
        if 'text' in file_metadata.get('@microsoft.graph.downloadUrl', '').lower() or 'json' in file_metadata.get('@microsoft.graph.downloadUrl', '').lower():
            content_response = requests.get(file_metadata['@microsoft.graph.downloadUrl'])
            content_response.raise_for_status()
            content = content_response.text
            return f"Content of {{file_metadata['name']}} (ID: {{file_id}}):\\n{{content}}"
        else:
            return f"File {{file_metadata['name']}} is binary (possibly an image or PDF). Use extract_image_text or extract_pdf_text to process."
    except Exception as e:
        return f"Error getting SharePoint file content: {{str(e)}}"

@mcp.tool()
def search_sharepoint_files(search_term: str) -> str:
    """Search for files in SharePoint site by name"""
    try:
        if not headers:
            return "SharePoint client not initialized"
        
        url = f"https://graph.microsoft.com/v1.0/sites/{{SITE_ID}}/drive/root/search(q='{{search_term}}')"
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        files = response.json().get('value', [])
        
        if not files:
            return f"No files found matching '{{search_term}}'"
        
        result = f"Files matching '{{search_term}}':\\n"
        for file in files:
            result += f"- {{file['name']}} (ID: {{file['id']}}, {{file['size']}} bytes)\\n"
        
        return result
    except Exception as e:
        return f"Error searching SharePoint files: {{str(e)}}"

@mcp.tool()
def extract_image_text(file_id: str, query: str = "Extract all text from the image") -> str:
    """Extract text from an image in SharePoint using Gemini LLM"""
    try:
        if not headers:
            return "SharePoint client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for image text extraction"

        # Get file metadata to check MIME type
        url = f"https://graph.microsoft.com/v1.0/sites/{{SITE_ID}}/drive/items/{{file_id}}"
        metadata_response = requests.get(url, headers=headers)
        metadata_response.raise_for_status()
        file_metadata = metadata_response.json()
        mime_type = file_metadata.get('file', {{}}).get('mimeType', '')
        if not any(mime_type.endswith(ext) for ext in ['image/png', 'image/jpeg', 'image/bmp']):
            return f"File {{file_metadata['name']}} is not a supported image format (PNG, JPG, JPEG, BMP)"

        # Download image
        content_response = requests.get(file_metadata['@microsoft.graph.downloadUrl'])
        content_response.raise_for_status()
        image_data = content_response.content

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare Gemini API request
        headers_gemini = {{
            'Content-Type': 'application/json'
        }}
        payload = {{
            "contents": [{{
                "parts": [
                    {{"text": query}},
                    {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                ]
            }}]
        }}

        # Call Gemini API with retry logic
        retries = 3
        for attempt in range(retries):
            try:
                response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers_gemini, json=payload)
                response.raise_for_status()
                break
            except requests.exceptions.HTTPError as e:
                if response.status_code == 429 and attempt < retries - 1:
                    sleep(2 ** attempt)
                    continue
                raise

        result = response.json()
        extracted_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', 'No text extracted')

        return f"Extracted text from {{file_metadata['name']}} (ID: {{file_id}}):\\n{{extracted_text}}\\n\\nSource: https://graph.microsoft.com/v1.0/sites/{{SITE_ID}}/drive/items/{{file_id}}"
    except Exception as e:
        return f"Error extracting text from image {{file_metadata.get('name', file_id)}}: {{str(e)}}"

@mcp.tool()
def extract_pdf_text(file_id: str, query: str = "Extract all text from the PDF") -> str:
    """Extract text from a PDF in SharePoint using Gemini LLM"""
    try:
        if not headers:
            return "SharePoint client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for PDF text extraction"

        # Get file metadata to check MIME type
        url = f"https://graph.microsoft.com/v1.0/sites/{{SITE_ID}}/drive/items/{{file_id}}"
        metadata_response = requests.get(url, headers=headers)
        metadata_response.raise_for_status()
        file_metadata = metadata_response.json()
        if file_metadata.get('file', {{}}).get('mimeType') != 'application/pdf':
            return f"File {{file_metadata['name']}} is not a PDF"

        # Download PDF
        content_response = requests.get(file_metadata['@microsoft.graph.downloadUrl'])
        content_response.raise_for_status()
        pdf_data = content_response.content

        # Convert PDF to images
        images = convert_from_bytes(pdf_data, first_page=1, last_page=10)  # Limit to first 10 pages
        extracted_texts = []

        for i, image in enumerate(images):
            # Resize image to reduce payload size
            image.thumbnail((1024, 1024))
            # Convert image to base64
            buffer = BytesIO()
            image.save(buffer, format="JPEG")
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Prepare Gemini API request
            headers_gemini = {{
                'Content-Type': 'application/json'
            }}
            payload = {{
                "contents": [{{
                    "parts": [
                        {{"text": f"{{query}} (Page {{i+1}})"}},
                        {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                    ]
                }}]
            }}

            # Call Gemini API with retry logic
            retries = 3
            for attempt in range(retries):
                try:
                    response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers_gemini, json=payload)
                    response.raise_for_status()
                    break
                except requests.exceptions.HTTPError as e:
                    if response.status_code == 429 and attempt < retries - 1:
                        sleep(2 ** attempt)
                        continue
                    raise

            result = response.json()
            page_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', f"No text extracted from page {{i+1}}")
            extracted_texts.append(f"Page {{i+1}}:\\n{{page_text}}\\n")

        result_str = f"Extracted text from {{file_metadata['name']}} (ID: {{file_id}}):\\n\\n{{'\\n'.join(extracted_texts)}}\\nSource: https://graph.microsoft.com/v1.0/sites/{{SITE_ID}}/drive/items/{{file_id}}"
        return result_str
    except Exception as e:
        return f"Error extracting text from PDF {{file_metadata.get('name', file_id)}}: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting SharePoint MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_onedrive_server(self, config: Dict) -> str:
        """Generate OneDrive MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic OneDrive MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP
from io import BytesIO
from PIL import Image
import base64
import json
import re
from pdf2image import convert_from_bytes
from time import sleep


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("OneDrive_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("onedrive_{server_id}", redirect_slashes=False, stateless_http=True)

# OneDrive Configuration
TENANT_ID = "{credentials.get('tenant_id', '')}"
CLIENT_ID = "{credentials.get('client_id', '')}"
CLIENT_SECRET = "{credentials.get('client_secret', '')}"
REFRESH_TOKEN = "{credentials.get('refresh_token', '')}"
DRIVE_ID = "{credentials.get('drive_id', '')}"
GEMINI_API_KEY = "{credentials.get('gemini_api_key', '')}"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

# Initialize OneDrive client (Microsoft Graph API)
try:
    authority = f"https://login.microsoftonline.com/{{TENANT_ID}}"
    app = ConfidentialClientApplication(
        CLIENT_ID,
        authority=authority,
        client_credential=CLIENT_SECRET
    )
    token_response = app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
    access_token = token_response.get('access_token')
    if not access_token:
        raise Exception("Failed to acquire access token")
    headers = {{
        'Authorization': f'Bearer {{access_token}}',
        'Content-Type': 'application/json'
    }}
    logger.info("OneDrive client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize OneDrive client: {{e}}")
    headers = None

@mcp.tool()
def list_onedrive_files(folder_path: str = "") -> str:
    """List files in OneDrive"""
    try:
        # Get access token
        token_url = f"https://login.microsoftonline.com/{{TENANT_ID}}/oauth2/v2.0/token"
        token_data = {{
            'grant_type': 'refresh_token',
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'refresh_token': REFRESH_TOKEN,
            'scope': 'https://graph.microsoft.com/Files.ReadWrite'
        }}

        token_response = requests.post(token_url, data=token_data)
        token_response.raise_for_status()
        access_token = token_response.json()['access_token']

        # List files
        headers = {{'Authorization': f'Bearer {{access_token}}'}}
        api_url = "https://graph.microsoft.com/v1.0/me/drive/root/children"

        response = requests.get(api_url, headers=headers)
        response.raise_for_status()

        files = response.json().get('value', [])
        if not files:
            return "No files found in OneDrive"

        result = "OneDrive Files:\\n"
        for file in files:
            result += f"- {{file['name']}} ({{file.get('size', 'N/A')}} bytes)\\n"

        return result
    except Exception as e:
        return f"Error listing OneDrive files: {{str(e)}}"

@mcp.tool()
def search_onedrive_files(search_term: str) -> str:
    """Search for files in OneDrive"""
    try:
        # Get access token
        token_url = f"https://login.microsoftonline.com/{{TENANT_ID}}/oauth2/v2.0/token"
        token_data = {{
            'grant_type': 'refresh_token',
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'refresh_token': REFRESH_TOKEN,
            'scope': 'https://graph.microsoft.com/Files.ReadWrite'
        }}

        token_response = requests.post(token_url, data=token_data)
        token_response.raise_for_status()
        access_token = token_response.json()['access_token']

        # Search files
        headers = {{'Authorization': f'Bearer {{access_token}}'}}
        search_url = f"https://graph.microsoft.com/v1.0/me/drive/search(q='{{search_term}}')"

        response = requests.get(search_url, headers=headers)
        response.raise_for_status()

        files = response.json().get('value', [])

        if not files:
            return f"No files found matching '{{search_term}}'"

        result = f"Files matching '{{search_term}}':\\n"
        for file in files:
            result += f"- {{file['name']}} ({{file.get('size', 'N/A')}} bytes)\\n"

        return result
    except Exception as e:
        return f"Error searching OneDrive files: {{str(e)}}"

        @mcp.tool()
def list_onedrive_files(folder_path: str = "") -> str:
    """List files in OneDrive"""
    try:
        if not headers:
            return "OneDrive client not initialized"
        
        url = f"https://graph.microsoft.com/v1.0/drives/{{DRIVE_ID}}/root:/{{folder_path}}:/children"
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        files = response.json().get('value', [])
        
        if not files:
            return f"No files found in OneDrive at path '{{folder_path}}'"
        
        result = f"Files in OneDrive:\\n"
        for file in files:
            result += f"- {{file['name']}} (ID: {{file['id']}}, {{file['size']}} bytes, {{file['lastModifiedDateTime']}})\\n"
        
        return result
    except Exception as e:
        return f"Error listing OneDrive files: {{str(e)}}"

@mcp.tool()
def get_onedrive_file_content(file_id: str) -> str:
    """Get content of a OneDrive file"""
    try:
        if not headers:
            return "OneDrive client not initialized"
        
        url = f"https://graph.microsoft.com/v1.0/drives/{{DRIVE_ID}}/items/{{file_id}}"
        metadata_response = requests.get(url, headers=headers)
        metadata_response.raise_for_status()
        file_metadata = metadata_response.json()
        
        if 'text' in file_metadata.get('@microsoft.graph.downloadUrl', '').lower() or 'json' in file_metadata.get('@microsoft.graph.downloadUrl', '').lower():
            content_response = requests.get(file_metadata['@microsoft.graph.downloadUrl'])
            content_response.raise_for_status()
            content = content_response.text
            return f"Content of {{file_metadata['name']}} (ID: {{file_id}}):\\n{{content}}"
        else:
            return f"File {{file_metadata['name']}} is binary (possibly an image or PDF). Use extract_image_text or extract_pdf_text to process."
    except Exception as e:
        return f"Error getting OneDrive file content: {{str(e)}}"

@mcp.tool()
def search_onedrive_files(search_term: str) -> str:
    """Search for files in OneDrive by name"""
    try:
        if not headers:
            return "OneDrive client not initialized"
        
        url = f"https://graph.microsoft.com/v1.0/drives/{{DRIVE_ID}}/root/search(q='{{search_term}}')"
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        files = response.json().get('value', [])
        
        if not files:
            return f"No files found matching '{{search_term}}'"
        
        result = f"Files matching '{{search_term}}':\\n"
        for file in files:
            result += f"- {{file['name']}} (ID: {{file['id']}}, {{file['size']}} bytes)\\n"
        
        return result
    except Exception as e:
        return f"Error searching OneDrive files: {{str(e)}}"

@mcp.tool()
def extract_image_text(file_id: str, query: str = "Extract all text from the image") -> str:
    """Extract text from an image in OneDrive using Gemini LLM"""
    try:
        if not headers:
            return "OneDrive client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for image text extraction"

        # Get file metadata to check MIME type
        url = f"https://graph.microsoft.com/v1.0/drives/{{DRIVE_ID}}/items/{{file_id}}"
        metadata_response = requests.get(url, headers=headers)
        metadata_response.raise_for_status()
        file_metadata = metadata_response.json()
        mime_type = file_metadata.get('file', {{}}).get('mimeType', '')
        if not any(mime_type.endswith(ext) for ext in ['image/png', 'image/jpeg', 'image/bmp']):
            return f"File {{file_metadata['name']}} is not a supported image format (PNG, JPG, JPEG, BMP)"

        # Download image
        content_response = requests.get(file_metadata['@microsoft.graph.downloadUrl'])
        content_response.raise_for_status()
        image_data = content_response.content

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare Gemini API request
        headers_gemini = {{
            'Content-Type': 'application/json'
        }}
        payload = {{
            "contents": [{{
                "parts": [
                    {{"text": query}},
                    {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                ]
            }}]
        }}

        # Call Gemini API with retry logic
        retries = 3
        for attempt in range(retries):
            try:
                response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers_gemini, json=payload)
                response.raise_for_status()
                break
            except requests.exceptions.HTTPError as e:
                if response.status_code == 429 and attempt < retries - 1:
                    sleep(2 ** attempt)
                    continue
                raise

        result = response.json()
        extracted_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', 'No text extracted')

        return f"Extracted text from {{file_metadata['name']}} (ID: {{file_id}}):\\n{{extracted_text}}\\n\\nSource: https://graph.microsoft.com/v1.0/drives/{{DRIVE_ID}}/items/{{file_id}}"
    except Exception as e:
        return f"Error extracting text from image {{file_metadata.get('name', file_id)}}: {{str(e)}}"

@mcp.tool()
def extract_pdf_text(file_id: str, query: str = "Extract all text from the PDF") -> str:
    """Extract text from a PDF in OneDrive using Gemini LLM"""
    try:
        if not headers:
            return "OneDrive client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for PDF text extraction"

        # Get file metadata to check MIME type
        url = f"https://graph.microsoft.com/v1.0/drives/{{DRIVE_ID}}/items/{{file_id}}"
        metadata_response = requests.get(url, headers=headers)
        metadata_response.raise_for_status()
        file_metadata = metadata_response.json()
        if file_metadata.get('file', {{}}).get('mimeType') != 'application/pdf':
            return f"File {{file_metadata['name']}} is not a PDF"

        # Download PDF
        content_response = requests.get(file_metadata['@microsoft.graph.downloadUrl'])
        content_response.raise_for_status()
        pdf_data = content_response.content

        # Convert PDF to images
        images = convert_from_bytes(pdf_data, first_page=1, last_page=10)  # Limit to first 10 pages
        extracted_texts = []

        for i, image in enumerate(images):
            # Resize image to reduce payload size
            image.thumbnail((1024, 1024))
            # Convert image to base64
            buffer = BytesIO()
            image.save(buffer, format="JPEG")
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Prepare Gemini API request
            headers_gemini = {{
                'Content-Type': 'application/json'
            }}
            payload = {{
                "contents": [{{
                    "parts": [
                        {{"text": f"{{query}} (Page {{i+1}})"}},
                        {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                    ]
                }}]
            }}

            # Call Gemini API with retry logic
            retries = 3
            for attempt in range(retries):
                try:
                    response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers_gemini, json=payload)
                    response.raise_for_status()
                    break
                except requests.exceptions.HTTPError as e:
                    if response.status_code == 429 and attempt < retries - 1:
                        sleep(2 ** attempt)
                        continue
                    raise

            result = response.json()
            page_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', f"No text extracted from page {{i+1}}")
            extracted_texts.append(f"Page {{i+1}}:\\n{{page_text}}\\n")

        result_str = f"Extracted text from {{file_metadata['name']}} (ID: {{file_id}}):\\n\\n{{'\\n'.join(extracted_texts)}}\\nSource: https://graph.microsoft.com/v1.0/drives/{{DRIVE_ID}}/items/{{file_id}}"
        return result_str
    except Exception as e:
        return f"Error extracting text from PDF {{file_metadata.get('name', file_id)}}: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting OneDrive MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_azure_blob_server(self, config: Dict) -> str:
        """Generate Azure Blob Storage MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic Azure Blob Storage MCP Server - {server_id}
"""
import logging
from azure.storage.blob import BlobServiceClient
from mcp.server.fastmcp import FastMCP
from io import BytesIO
from PIL import Image
import base64
import json
import re
import requests
from pdf2image import convert_from_bytes
from time import sleep

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AzureBlob_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("azure_blob_{server_id}", redirect_slashes=False, stateless_http=True)

# Azure Blob Configuration
STORAGE_ACCOUNT_NAME = "{credentials.get('storage_account_name', '')}"
ACCESS_KEY = "{credentials.get('access_key', '')}"
CONTAINER_NAME = "{credentials.get('container_name', '')}"
ENDPOINT_URL = "{credentials.get('endpoint_url', f'https://{credentials.get("storage_account_name", "")}.blob.core.windows.net')}"
CONNECTION_STRING = "{credentials.get('connection_string', '')}"
GEMINI_API_KEY = "{credentials.get('gemini_api_key', '')}"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

# Initialize Azure Blob client
try:
    blob_service_client = BlobServiceClient(
        account_url=ENDPOINT_URL,
        credential=ACCESS_KEY,
        connection_string=CONNECTION_STRING
    )
    container_client = blob_service_client.get_container_client(CONTAINER_NAME)
    logger.info("Azure Blob client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Azure Blob client: {{e}}")
    blob_service_client = None
    container_client = None

@mcp.tool()
def list_azure_blobs(prefix: str = "") -> str:
    """List blobs in Azure container"""
    try:
        if not container_client:
            return "Azure Blob client not initialized"

        blobs = list(container_client.list_blobs(name_starts_with=prefix))

        if not blobs:
            return f"No blobs found in container {{CONTAINER_NAME}} with prefix '{{prefix}}'"

        result = f"Blobs in {{CONTAINER_NAME}}:\\n"
        for blob in blobs:
            result += f"- {{blob.name}} ({{blob.size}} bytes, {{blob.last_modified}})\\n"

        return result
    except Exception as e:
        return f"Error listing Azure blobs: {{str(e)}}"

@mcp.tool()
def get_azure_blob_content(blob_name: str) -> str:
    """Get content of Azure blob"""
    try:
        if not container_client:
            return "Azure Blob client not initialized"

        blob_client = container_client.get_blob_client(blob_name)
        content = blob_client.download_blob().readall().decode('utf-8')
        return f"Content of {{blob_name}}:\\n{{content}}"
    except Exception as e:
        return f"Error getting Azure blob content: {{str(e)}}"

@mcp.tool()
def search_azure_blobs(search_term: str) -> str:
    """Search for blobs in Azure container by name"""
    try:
        if not container_client:
            return "Azure Blob client not initialized"

        blobs = list(container_client.list_blobs())
        matching_blobs = [blob for blob in blobs if search_term.lower() in blob.name.lower()]

        if not matching_blobs:
            return f"No blobs found matching '{{search_term}}'"

        result = f"Blobs matching '{{search_term}}':\\n"
        for blob in matching_blobs:
            result += f"- {{blob.name}} ({{blob.size}} bytes)\\n"

        return result
    except Exception as e:
        return f"Error searching Azure blobs: {{str(e)}}"

@mcp.tool()
def extract_image_text(key: str, query: str = "Extract all text from the image") -> str:
    """Extract text from an image in Azure Blob using Gemini LLM"""
    try:
        if not container_client:
            return "Azure Blob client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for image text extraction"

        # Check if the file is an image
        if not any(key.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.bmp']):
            return f"File {{key}} is not a supported image format (PNG, JPG, JPEG, BMP)"

        # Download image
        blob_client = container_client.get_blob_client(key)
        image_data = blob_client.download_blob().readall()

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare Gemini API request
        headers = {{
            'Content-Type': 'application/json'
        }}
        payload = {{
            "contents": [{{
                "parts": [
                    {{"text": query}},
                    {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                ]
            }}]
        }}

        # Call Gemini API with retry logic
        retries = 3
        for attempt in range(retries):
            try:
                response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers, json=payload)
                response.raise_for_status()
                break
            except requests.exceptions.HTTPError as e:
                if response.status_code == 429 and attempt < retries - 1:
                    sleep(2 ** attempt)
                    continue
                raise

        result = response.json()
        extracted_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', 'No text extracted')

        return f"Extracted text from {{key}}:\\n{{extracted_text}}\\n\\nSource: azure://{{CONTAINER_NAME}}/{{key}}"
    except Exception as e:
        return f"Error extracting text from image {{key}}: {{str(e)}}"

@mcp.tool()
def extract_pdf_text(key: str, query: str = "Extract all text from the PDF") -> str:
    """Extract text from a PDF in Azure Blob using Gemini LLM"""
    try:
        if not container_client:
            return "Azure Blob client not initialized"
        if not GEMINI_API_KEY:
            return "Gemini API key is required for PDF text extraction"

        # Check if the file is a PDF
        if not key.lower().endswith('.pdf'):
            return f"File {{key}} is not a PDF"

        # Download PDF
        blob_client = container_client.get_blob_client(key)
        pdf_data = blob_client.download_blob().readall()

        # Convert PDF to images
        images = convert_from_bytes(pdf_data, first_page=1, last_page=10)  # Limit to first 10 pages
        extracted_texts = []

        for i, image in enumerate(images):
            # Resize image to reduce payload size
            image.thumbnail((1024, 1024))
            # Convert image to base64
            buffer = BytesIO()
            image.save(buffer, format="JPEG")
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Prepare Gemini API request
            headers = {{
                'Content-Type': 'application/json'
            }}
            payload = {{
                "contents": [{{
                    "parts": [
                        {{"text": f"{{query}} (Page {{i+1}})"}},
                        {{"inline_data": {{"mime_type": "image/jpeg", "data": image_base64}}}}
                    ]
                }}]
            }}

            # Call Gemini API with retry logic
            retries = 3
            for attempt in range(retries):
                try:
                    response = requests.post(f"{{GEMINI_API_URL}}?key={{GEMINI_API_KEY}}", headers=headers, json=payload)
                    response.raise_for_status()
                    break
                except requests.exceptions.HTTPError as e:
                    if response.status_code == 429 and attempt < retries - 1:
                        sleep(2 ** attempt)
                        continue
                    raise

            result = response.json()
            page_text = result.get('candidates', [{{}}])[0].get('content', {{}}).get('parts', [{{}}])[0].get('text', f"No text extracted from page {{i+1}}")
            extracted_texts.append(f"Page {{i+1}}:\\n{{page_text}}\\n")

        result_str = f"Extracted text from {{key}}:\\n\\n{{'\\n'.join(extracted_texts)}}\\nSource: azure://{{CONTAINER_NAME}}/{{key}}"
        return result_str
    except Exception as e:
        return f"Error extracting text from PDF {{key}}: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Azure Blob MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_database_server(self, config: Dict) -> str:
        """Generate database MCP server code"""
        service = config["service"]
        if service == "postgres":
            return self._generate_postgres_server(config)
        elif service == "mysql":
            return self._generate_mysql_server(config)
        elif service == "mongodb":
            return self._generate_mongodb_server(config)
        elif service == "redis":
            return self._generate_redis_server(config)
        elif service == "sql_server":
            return self._generate_sql_server_server(config)
        else:
            raise ValueError(f"Unsupported database service: {service}")

    def _generate_postgres_server(self, config: Dict) -> str:
        """Generate PostgreSQL MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic PostgreSQL MCP Server - {server_id}
"""
import logging
import psycopg2
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("PostgreSQL_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("postgres_{server_id}", redirect_slashes=False, stateless_http=True)

# PostgreSQL Configuration
HOST = "{credentials.get('host', 'localhost')}"
PORT = {credentials.get('port', 5432)}
USERNAME = "{credentials.get('username', '')}"
PASSWORD = "{credentials.get('password', '')}"
DATABASE_NAME = "{credentials.get('database_name', '')}"
CONNECTION_STRING = "{credentials.get('connection_string', '')}"

@mcp.tool()
def query_postgres(sql_query: str) -> str:
    """Execute SQL query on PostgreSQL database"""
    try:
        if CONNECTION_STRING:
            conn = psycopg2.connect(CONNECTION_STRING)
        else:
            conn = psycopg2.connect(
                host=HOST,
                port=PORT,
                user=USERNAME,
                password=PASSWORD,
                database=DATABASE_NAME
            )

        cursor = conn.cursor()
        cursor.execute(sql_query)

        if sql_query.strip().upper().startswith('SELECT'):
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            result = f"Query Results:\\n"
            result += f"Columns: {{', '.join(columns)}}\\n"
            result += "-" * 50 + "\\n"

            for row in results:
                result += f"{{', '.join(str(val) for val in row)}}\\n"

            return result
        else:
            conn.commit()
            return f"Query executed successfully. Rows affected: {{cursor.rowcount}}"

    except Exception as e:
        return f"Error executing PostgreSQL query: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

@mcp.tool()
def list_postgres_tables() -> str:
    """List all tables in PostgreSQL database"""
    try:
        if CONNECTION_STRING:
            conn = psycopg2.connect(CONNECTION_STRING)
        else:
            conn = psycopg2.connect(
                host=HOST,
                port=PORT,
                user=USERNAME,
                password=PASSWORD,
                database=DATABASE_NAME
            )

        cursor = conn.cursor()
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
        """)

        tables = cursor.fetchall()

        if not tables:
            return "No tables found in database"

        result = "Tables in database:\\n"
        for table in tables:
            result += f"- {{table[0]}}\\n"

        return result

    except Exception as e:
        return f"Error listing PostgreSQL tables: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("Starting PostgreSQL MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_mysql_server(self, config: Dict) -> str:
        """Generate MySQL MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic MySQL MCP Server - {server_id}
"""
import logging
import mysql.connector
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MySQL_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("mysql_{server_id}", redirect_slashes=False, stateless_http=True)

# MySQL Configuration
HOST = "{credentials.get('host', 'localhost')}"
PORT = {credentials.get('port', 3306)}
USERNAME = "{credentials.get('username', '')}"
PASSWORD = "{credentials.get('password', '')}"
DATABASE_NAME = "{credentials.get('database_name', '')}"

@mcp.tool()
def query_mysql(sql_query: str) -> str:
    """Execute SQL query on MySQL database"""
    try:
        conn = mysql.connector.connect(
            host=HOST,
            port=PORT,
            user=USERNAME,
            password=PASSWORD,
            database=DATABASE_NAME
        )

        cursor = conn.cursor()
        cursor.execute(sql_query)

        if sql_query.strip().upper().startswith('SELECT'):
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            result = f"Query Results:\\n"
            result += f"Columns: {{', '.join(columns)}}\\n"
            result += "-" * 50 + "\\n"

            for row in results:
                result += f"{{', '.join(str(val) for val in row)}}\\n"

            return result
        else:
            conn.commit()
            return f"Query executed successfully. Rows affected: {{cursor.rowcount}}"

    except Exception as e:
        return f"Error executing MySQL query: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

@mcp.tool()
def list_mysql_tables() -> str:
    """List all tables in MySQL database"""
    try:
        conn = mysql.connector.connect(
            host=HOST,
            port=PORT,
            user=USERNAME,
            password=PASSWORD,
            database=DATABASE_NAME
        )

        cursor = conn.cursor()
        cursor.execute("SHOW TABLES")

        tables = cursor.fetchall()

        if not tables:
            return "No tables found in database"

        result = "Tables in database:\\n"
        for table in tables:
            result += f"- {{table[0]}}\\n"

        return result

    except Exception as e:
        return f"Error listing MySQL tables: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("Starting MySQL MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_mongodb_server(self, config: Dict) -> str:
        """Generate MongoDB MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic MongoDB MCP Server - {server_id}
"""
import logging
from pymongo import MongoClient
from mcp.server.fastmcp import FastMCP
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MongoDB_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("mongodb_{server_id}", redirect_slashes=False, stateless_http=True)

# MongoDB Configuration
HOST = "{credentials.get('host', 'localhost')}"
PORT = {credentials.get('port', 27017)}
USERNAME = "{credentials.get('username', '')}"
PASSWORD = "{credentials.get('password', '')}"
DATABASE_NAME = "{credentials.get('database_name', '')}"
CONNECTION_STRING = "{credentials.get('connection_string', '')}"

@mcp.tool()
def query_mongodb(collection_name: str, query: str = "{{}}", limit: int = 10) -> str:
    """Query MongoDB collection"""
    try:
        if CONNECTION_STRING:
            client = MongoClient(CONNECTION_STRING)
        else:
            if USERNAME and PASSWORD:
                client = MongoClient(f"mongodb://{{USERNAME}}:{{PASSWORD}}@{{HOST}}:{{PORT}}/{{DATABASE_NAME}}")
            else:
                client = MongoClient(f"mongodb://{{HOST}}:{{PORT}}")

        db = client[DATABASE_NAME]
        collection = db[collection_name]

        # Parse query string to dict
        query_dict = json.loads(query) if query != "{{}}" else {{}}

        results = list(collection.find(query_dict).limit(limit))

        if not results:
            return f"No documents found in collection {{collection_name}}"

        result = f"Documents from {{collection_name}}:\\n"
        for i, doc in enumerate(results, 1):
            # Convert ObjectId to string for JSON serialization
            doc['_id'] = str(doc['_id'])
            result += f"{{i}}. {{json.dumps(doc, indent=2)}}\\n"

        return result

    except Exception as e:
        return f"Error querying MongoDB: {{str(e)}}"
    finally:
        if 'client' in locals():
            client.close()

@mcp.tool()
def list_mongodb_collections() -> str:
    """List all collections in MongoDB database"""
    try:
        if CONNECTION_STRING:
            client = MongoClient(CONNECTION_STRING)
        else:
            if USERNAME and PASSWORD:
                client = MongoClient(f"mongodb://{{USERNAME}}:{{PASSWORD}}@{{HOST}}:{{PORT}}/{{DATABASE_NAME}}")
            else:
                client = MongoClient(f"mongodb://{{HOST}}:{{PORT}}")

        db = client[DATABASE_NAME]
        collections = db.list_collection_names()

        if not collections:
            return "No collections found in database"

        result = "Collections in database:\\n"
        for collection in collections:
            result += f"- {{collection}}\\n"

        return result

    except Exception as e:
        return f"Error listing MongoDB collections: {{str(e)}}"
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    logger.info("Starting MongoDB MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_redis_server(self, config: Dict) -> str:
        """Generate Redis MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic Redis MCP Server - {server_id}
"""
import logging
import redis
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Redis_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("redis_{server_id}", redirect_slashes=False, stateless_http=True)

# Redis Configuration
HOST = "{credentials.get('host', 'localhost')}"
PORT = {credentials.get('port', 6379)}
PASSWORD = "{credentials.get('password', '')}"
DATABASE_NUM = {credentials.get('database_name', 0)}

@mcp.tool()
def redis_get(key: str) -> str:
    """Get value from Redis"""
    try:
        r = redis.Redis(
            host=HOST,
            port=PORT,
            password=PASSWORD if PASSWORD else None,
            db=DATABASE_NUM,
            decode_responses=True
        )

        value = r.get(key)
        if value is None:
            return f"Key '{{key}}' not found in Redis"

        return f"Value for '{{key}}': {{value}}"

    except Exception as e:
        return f"Error getting Redis key: {{str(e)}}"

@mcp.tool()
def redis_keys(pattern: str = "*") -> str:
    """List Redis keys matching pattern"""
    try:
        r = redis.Redis(
            host=HOST,
            port=PORT,
            password=PASSWORD if PASSWORD else None,
            db=DATABASE_NUM,
            decode_responses=True
        )

        keys = r.keys(pattern)

        if not keys:
            return f"No keys found matching pattern '{{pattern}}'"

        result = f"Keys matching '{{pattern}}':\\n"
        for key in keys:
            result += f"- {{key}}\\n"

        return result

    except Exception as e:
        return f"Error listing Redis keys: {{str(e)}}"

@mcp.tool()
def redis_info() -> str:
    """Get Redis server information"""
    try:
        r = redis.Redis(
            host=HOST,
            port=PORT,
            password=PASSWORD if PASSWORD else None,
            db=DATABASE_NUM,
            decode_responses=True
        )

        info = r.info()

        result = "Redis Server Info:\\n"
        result += f"Redis Version: {{info.get('redis_version', 'N/A')}}\\n"
        result += f"Connected Clients: {{info.get('connected_clients', 'N/A')}}\\n"
        result += f"Used Memory: {{info.get('used_memory_human', 'N/A')}}\\n"
        result += f"Total Keys: {{info.get('db0', {{}}).get('keys', 0) if 'db0' in info else 0}}\\n"

        return result

    except Exception as e:
        return f"Error getting Redis info: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Redis MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_sql_server_server(self, config: Dict) -> str:
        """Generate SQL Server MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic SQL Server MCP Server - {server_id}
"""
import logging
import pyodbc
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("SQLServer_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("sqlserver_{server_id}", redirect_slashes=False, stateless_http=True)

# SQL Server Configuration
HOST = "{credentials.get('host', 'localhost')}"
PORT = {credentials.get('port', 1433)}
USERNAME = "{credentials.get('username', '')}"
PASSWORD = "{credentials.get('password', '')}"
DATABASE_NAME = "{credentials.get('database_name', '')}"
CONNECTION_STRING = "{credentials.get('connection_string', '')}"

@mcp.tool()
def query_sqlserver(sql_query: str) -> str:
    """Execute SQL query on SQL Server database"""
    try:
        if CONNECTION_STRING:
            conn = pyodbc.connect(CONNECTION_STRING)
        else:
            conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={{HOST}},{{PORT}};DATABASE={{DATABASE_NAME}};UID={{USERNAME}};PWD={{PASSWORD}}"
            conn = pyodbc.connect(conn_str)

        cursor = conn.cursor()
        cursor.execute(sql_query)

        if sql_query.strip().upper().startswith('SELECT'):
            results = cursor.fetchall()
            columns = [column[0] for column in cursor.description]

            result = f"Query Results:\\n"
            result += f"Columns: {{', '.join(columns)}}\\n"
            result += "-" * 50 + "\\n"

            for row in results:
                result += f"{{', '.join(str(val) for val in row)}}\\n"

            return result
        else:
            conn.commit()
            return f"Query executed successfully. Rows affected: {{cursor.rowcount}}"

    except Exception as e:
        return f"Error executing SQL Server query: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

@mcp.tool()
def list_sqlserver_tables() -> str:
    """List all tables in SQL Server database"""
    try:
        if CONNECTION_STRING:
            conn = pyodbc.connect(CONNECTION_STRING)
        else:
            conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={{HOST}},{{PORT}};DATABASE={{DATABASE_NAME}};UID={{USERNAME}};PWD={{PASSWORD}}"
            conn = pyodbc.connect(conn_str)

        cursor = conn.cursor()
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_type = 'BASE TABLE'
        """)

        tables = cursor.fetchall()

        if not tables:
            return "No tables found in database"

        result = "Tables in database:\\n"
        for table in tables:
            result += f"- {{table[0]}}\\n"

        return result

    except Exception as e:
        return f"Error listing SQL Server tables: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("Starting SQL Server MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_devops_server(self, config: Dict) -> str:
        """Generate DevOps tools MCP server code"""
        service = config["service"]
        if service == "jira":
            return self._generate_jira_server(config)
        elif service == "azure_devops":
            return self._generate_azure_devops_server(config)
        else:
            raise ValueError(f"Unsupported DevOps service: {service}")

    def _generate_jira_server(self, config: Dict) -> str:
        """Generate Jira MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic Jira MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP
import base64

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Jira_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("jira_{server_id}", redirect_slashes=False, stateless_http=True)

# Jira Configuration
API_URL = "{credentials.get('api_url', '')}"
USERNAME = "{credentials.get('username', '')}"
API_TOKEN = "{credentials.get('api_token', '')}"
PROJECT_KEY = "{credentials.get('project_key', '')}"

@mcp.tool()
def search_jira_issues(jql: str = "", max_results: int = 10) -> str:
    """Search Jira issues using JQL"""
    try:
        auth_string = base64.b64encode(f"{{USERNAME}}:{{API_TOKEN}}".encode()).decode()
        headers = {{
            'Authorization': f'Basic {{auth_string}}',
            'Content-Type': 'application/json'
        }}

        if not jql:
            jql = f"project = {{PROJECT_KEY}} ORDER BY created DESC" if PROJECT_KEY else "ORDER BY created DESC"

        url = f"{{API_URL}}/rest/api/3/search"
        params = {{
            'jql': jql,
            'maxResults': max_results,
            'fields': 'summary,status,assignee,created,priority'
        }}

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        issues = data.get('issues', [])

        if not issues:
            return "No issues found"

        result = f"Jira Issues ({{len(issues)}} found):\\n"
        for issue in issues:
            fields = issue.get('fields', {{}})
            result += f"- {{issue['key']}}: {{fields.get('summary', 'No summary')}}\\n"
            result += f"  Status: {{fields.get('status', {{}}).get('name', 'Unknown')}}\\n"
            result += f"  Assignee: {{fields.get('assignee', {{}}).get('displayName', 'Unassigned') if fields.get('assignee') else 'Unassigned'}}\\n"
            result += f"  Priority: {{fields.get('priority', {{}}).get('name', 'Unknown') if fields.get('priority') else 'Unknown'}}\\n\\n"

        return result

    except Exception as e:
        return f"Error searching Jira issues: {{str(e)}}"

@mcp.tool()
def get_jira_issue(issue_key: str) -> str:
    """Get details of a specific Jira issue"""
    try:
        auth_string = base64.b64encode(f"{{USERNAME}}:{{API_TOKEN}}".encode()).decode()
        headers = {{
            'Authorization': f'Basic {{auth_string}}',
            'Content-Type': 'application/json'
        }}

        url = f"{{API_URL}}/rest/api/3/issue/{{issue_key}}"

        response = requests.get(url, headers=headers)
        response.raise_for_status()

        issue = response.json()
        fields = issue.get('fields', {{}})

        result = f"Jira Issue {{issue_key}}:\\n"
        result += f"Summary: {{fields.get('summary', 'No summary')}}\\n"
        result += f"Description: {{fields.get('description', 'No description')}}\\n"
        result += f"Status: {{fields.get('status', {{}}).get('name', 'Unknown')}}\\n"
        result += f"Assignee: {{fields.get('assignee', {{}}).get('displayName', 'Unassigned') if fields.get('assignee') else 'Unassigned'}}\\n"
        result += f"Reporter: {{fields.get('reporter', {{}}).get('displayName', 'Unknown') if fields.get('reporter') else 'Unknown'}}\\n"
        result += f"Priority: {{fields.get('priority', {{}}).get('name', 'Unknown') if fields.get('priority') else 'Unknown'}}\\n"
        result += f"Created: {{fields.get('created', 'Unknown')}}\\n"

        return result

    except Exception as e:
        return f"Error getting Jira issue: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Jira MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_azure_devops_server(self, config: Dict) -> str:
        """Generate Azure DevOps MCP server code (placeholder)"""
        server_id = config["server_id"]
        return f'''#!/usr/bin/env python3
"""
Dynamic Azure DevOps MCP Server - {server_id} (Placeholder)
"""
import logging
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AzureDevOps_MCP_{server_id}")

mcp = FastMCP("azure_devops_{server_id}", redirect_slashes=False, stateless_http=True)

@mcp.tool()
def placeholder_tool() -> str:
    """Placeholder tool for Azure DevOps"""
    return "Azure DevOps MCP server placeholder - implementation needed"

if __name__ == "__main__":
    logger.info("Starting Azure DevOps MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_git_server(self, config: Dict) -> str:
        """Generate Git service MCP server code"""
        service = config["service"]
        if service == "github":
            return self._generate_github_server(config)
        elif service == "bitbucket":
            return self._generate_bitbucket_server(config)
        elif service == "azure_repos":
            return self._generate_azure_repos_server(config)
        else:
            raise ValueError(f"Unsupported Git service: {service}")

    def _generate_github_server(self, config: Dict) -> str:
        """Generate GitHub MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic GitHub MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GitHub_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("github_{server_id}", redirect_slashes=False, stateless_http=True)

# GitHub Configuration
USERNAME = "{credentials.get('username', '')}"
TOKEN = "{credentials.get('token', '')}"
REPOSITORY_URL = "{credentials.get('repository_url', '')}"
ORGANIZATION = "{credentials.get('organization', '')}"

@mcp.tool()
def list_github_repos(org: str = "") -> str:
    """List GitHub repositories"""
    try:
        headers = {{
            'Authorization': f'token {{TOKEN}}',
            'Accept': 'application/vnd.github.v3+json'
        }}

        if org or ORGANIZATION:
            url = f"https://api.github.com/orgs/{{org or ORGANIZATION}}/repos"
        else:
            url = f"https://api.github.com/users/{{USERNAME}}/repos"

        response = requests.get(url, headers=headers)
        response.raise_for_status()

        repos = response.json()

        if not repos:
            return "No repositories found"

        result = f"GitHub Repositories ({{len(repos)}} found):\\n"
        for repo in repos:
            result += f"- {{repo['name']}} ({{repo['language'] or 'Unknown'}})\\n"
            result += f"  Description: {{repo['description'] or 'No description'}}\\n"
            result += f"  Stars: {{repo['stargazers_count']}} | Forks: {{repo['forks_count']}}\\n"
            result += f"  URL: {{repo['html_url']}}\\n\\n"

        return result

    except Exception as e:
        return f"Error listing GitHub repositories: {{str(e)}}"

@mcp.tool()
def search_github_code(query: str, repo: str = "") -> str:
    """Search code in GitHub repositories"""
    try:
        headers = {{
            'Authorization': f'token {{TOKEN}}',
            'Accept': 'application/vnd.github.v3+json'
        }}

        search_query = f"{{query}}"
        if repo:
            search_query += f" repo:{{repo}}"
        elif ORGANIZATION:
            search_query += f" org:{{ORGANIZATION}}"

        url = f"https://api.github.com/search/code"
        params = {{'q': search_query, 'per_page': 10}}

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        items = data.get('items', [])

        if not items:
            return f"No code found matching '{{query}}'"

        result = f"GitHub Code Search Results for '{{query}}' ({{len(items)}} found):\\n"
        for item in items:
            result += f"- {{item['name']}} in {{item['repository']['full_name']}}\\n"
            result += f"  Path: {{item['path']}}\\n"
            result += f"  URL: {{item['html_url']}}\\n\\n"

        return result

    except Exception as e:
        return f"Error searching GitHub code: {{str(e)}}"

@mcp.tool()
def get_github_issues(repo: str, state: str = "open") -> str:
    """Get GitHub issues for a repository"""
    try:
        headers = {{
            'Authorization': f'token {{TOKEN}}',
            'Accept': 'application/vnd.github.v3+json'
        }}

        url = f"https://api.github.com/repos/{{repo}}/issues"
        params = {{'state': state, 'per_page': 10}}

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        issues = response.json()

        if not issues:
            return f"No {{state}} issues found in {{repo}}"

        result = f"GitHub Issues in {{repo}} ({{state}}) - {{len(issues)}} found:\\n"
        for issue in issues:
            result += f"- #{{issue['number']}}: {{issue['title']}}\\n"
            result += f"  State: {{issue['state']}} | Comments: {{issue['comments']}}\\n"
            result += f"  Created: {{issue['createdAt']}}\\n"
            result += f"  URL: {{issue['html_url']}}\\n\\n"

        return result

    except Exception as e:
        return f"Error getting GitHub issues: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting GitHub MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_bitbucket_server(self, config: Dict) -> str:
        """Generate Bitbucket MCP server code (placeholder)"""
        server_id = config["server_id"]
        return f'''#!/usr/bin/env python3
"""
Dynamic Bitbucket MCP Server - {server_id} (Placeholder)
"""
import logging
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Bitbucket_MCP_{server_id}")

mcp = FastMCP("bitbucket_{server_id}", redirect_slashes=False, stateless_http=True)

@mcp.tool()
def placeholder_tool() -> str:
    """Placeholder tool for Bitbucket"""
    return "Bitbucket MCP server placeholder - implementation needed"

if __name__ == "__main__":
    logger.info("Starting Bitbucket MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_azure_repos_server(self, config: Dict) -> str:
        """Generate Azure Repos MCP server code (placeholder)"""
        server_id = config["server_id"]
        return f'''#!/usr/bin/env python3
"""
Dynamic Azure Repos MCP Server - {server_id} (Placeholder)
"""
import logging
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AzureRepos_MCP_{server_id}")

mcp = FastMCP("azure_repos_{server_id}", redirect_slashes=False, stateless_http=True)

@mcp.tool()
def placeholder_tool() -> str:
    """Placeholder tool for Azure Repos"""
    return "Azure Repos MCP server placeholder - implementation needed"

if __name__ == "__main__":
    logger.info("Starting Azure Repos MCP server with stdio transport")
    mcp.run(transport="stdio")
'''

    def _generate_public_server(self, config: Dict) -> str:
        """Generate public service MCP server code"""
        service = config["service"]
        credentials = config["credentials"]
        server_id = config["server_id"]

        if service == "airbnb":
            return f'''#!/usr/bin/env python3
"""
Dynamic Hotel Search MCP Server - {server_id}
Uses free public APIs for hotel search and recommendations
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP
import json
from datetime import datetime, timedelta
import urllib.parse

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Hotels_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("hotels_{server_id}", redirect_slashes=False, stateless_http=True)

@mcp.tool()
def search_hotels(location: str, checkin_date: str = "", checkout_date: str = "", adults: int = 2) -> str:
    """Search for hotels in a specific location using free public APIs

    Args:
        location: City or area name (e.g., "Mumbai", "Juhu Beach Mumbai")
        checkin_date: Check-in date in YYYY-MM-DD format (optional, defaults to tomorrow)
        checkout_date: Check-out date in YYYY-MM-DD format (optional, defaults to day after checkin)
        adults: Number of adults (default: 2)
    """
    try:
        # Set default dates if not provided
        if not checkin_date:
            checkin_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
        if not checkout_date:
            checkout_date = (datetime.strptime(checkin_date, "%Y-%m-%d") + timedelta(days=1)).strftime("%Y-%m-%d")

        # First get coordinates for the location using a geocoding service
        geocode_url = f"https://nominatim.openstreetmap.org/search?format=json&q={{urllib.parse.quote(location)}}&limit=1"

        headers = {{
            'User-Agent': 'HotelSearchMCP/1.0'
        }}

        geocode_response = requests.get(geocode_url, headers=headers)
        geocode_response.raise_for_status()
        geocode_data = geocode_response.json()

        if not geocode_data:
            return f"Location '{{location}}' not found. Please try a more specific location name."

        lat = float(geocode_data[0]['lat'])
        lon = float(geocode_data[0]['lon'])
        display_name = geocode_data[0].get('display_name', location)

        # Search for accommodations using Overpass API (OpenStreetMap data)
        overpass_url = "https://overpass-api.de/api/interpreter"

        # Query for hotels, hostels, and guesthouses within 10km radius
        overpass_query = f"""
        [out:json][timeout:25];
        (
          node["tourism"="hotel"](around:10000,{{lat}},{{lon}});
          node["tourism"="hostel"](around:10000,{{lat}},{{lon}});
          node["tourism"="guest_house"](around:10000,{{lat}},{{lon}});
          node["tourism"="motel"](around:10000,{{lat}},{{lon}});
          way["tourism"="hotel"](around:10000,{{lat}},{{lon}});
          way["tourism"="hostel"](around:10000,{{lat}},{{lon}});
          way["tourism"="guest_house"](around:10000,{{lat}},{{lon}});
          way["tourism"="motel"](around:10000,{{lat}},{{lon}});
        );
        out center meta;
        """

        overpass_response = requests.post(overpass_url, data={{'data': overpass_query}}, headers=headers)
        overpass_response.raise_for_status()
        overpass_data = overpass_response.json()

        hotels = overpass_data.get('elements', [])

        # Process hotels and extract information
        processed_hotels = []
        for hotel in hotels:
            tags = hotel.get('tags', {{}})
            if 'name' in tags:
                # Get coordinates (handle both nodes and ways)
                if hotel.get('type') == 'way' and 'center' in hotel:
                    hotel_lat = hotel['center']['lat']
                    hotel_lon = hotel['center']['lon']
                else:
                    hotel_lat = hotel.get('lat', lat)
                    hotel_lon = hotel.get('lon', lon)

                # Calculate distance from search center
                distance = ((hotel_lat - lat) ** 2 + (hotel_lon - lon) ** 2) ** 0.5 * 111  # Rough km conversion

                processed_hotels.append({{
                    'name': tags.get('name', 'Unnamed Hotel'),
                    'type': tags.get('tourism', 'hotel').replace('_', ' ').title(),
                    'address': tags.get('addr:full', tags.get('addr:street', tags.get('addr:city', 'Address not available'))),
                    'phone': tags.get('phone', tags.get('contact:phone', '')),
                    'website': tags.get('website', tags.get('contact:website', '')),
                    'email': tags.get('email', tags.get('contact:email', '')),
                    'stars': tags.get('stars', ''),
                    'distance': distance,
                    'lat': hotel_lat,
                    'lon': hotel_lon
                }})

        # Sort by distance and limit results
        processed_hotels.sort(key=lambda x: x['distance'])
        top_hotels = processed_hotels[:15]

        if not top_hotels:
            # Fallback: provide comprehensive booking platform suggestions
            result = f"No specific hotel data found in OpenStreetMap for {{display_name}}.\\n\\n"
            result += f"🏨 **SEARCH ON MAJOR BOOKING PLATFORMS:**\\n\\n"
            result += f"**Hotels & Resorts:**\\n"
            result += f"• Booking.com: https://www.booking.com/searchresults.html?ss={{urllib.parse.quote(location)}}&checkin={{checkin_date}}&checkout={{checkout_date}}&group_adults={{adults}}\\n"
            result += f"• Hotels.com: https://www.hotels.com/search.do?destination={{urllib.parse.quote(location)}}&startDate={{checkin_date}}&endDate={{checkout_date}}&rooms={{adults}}\\n"
            result += f"• Expedia: https://www.expedia.com/Hotel-Search?destination={{urllib.parse.quote(location)}}&startDate={{checkin_date}}&endDate={{checkout_date}}&rooms={{adults}}\\n"
            result += f"• Agoda: https://www.agoda.com/search?city={{urllib.parse.quote(location)}}&checkIn={{checkin_date}}&checkOut={{checkout_date}}&rooms={{adults}}\\n"
            result += f"• Trivago: https://www.trivago.com/{{urllib.parse.quote(location)}}?search;dr-{{checkin_date}}-{{checkout_date}}\\n\\n"

            result += f"**Alternative Accommodations:**\\n"
            result += f"• Airbnb: https://www.airbnb.com/s/{{urllib.parse.quote(location)}}/homes?checkin={{checkin_date}}&checkout={{checkout_date}}&adults={{adults}}\\n"
            result += f"• Vrbo: https://www.vrbo.com/search?destination={{urllib.parse.quote(location)}}&startDate={{checkin_date}}&endDate={{checkout_date}}&adults={{adults}}\\n"
            result += f"• Hostelworld: https://www.hostelworld.com/search?search_keywords={{urllib.parse.quote(location)}}&date_from={{checkin_date}}&date_to={{checkout_date}}\\n\\n"

            result += f"**Search Details:**\\n"
            result += f"• Location: {{display_name}}\\n"
            result += f"• Check-in: {{checkin_date}}\\n"
            result += f"• Check-out: {{checkout_date}}\\n"
            result += f"• Guests: {{adults}} adults\\n\\n"
            result += f"Source: Free OpenStreetMap + Booking Platform Links\\n"
            result += f"OSM URL: https://www.openstreetmap.org/search?query={{urllib.parse.quote(location)}}\\n"
            result += f"Data API: https://overpass-api.de/api/interpreter"


            return result

        result = f"Hotels and accommodations in {{display_name}}:\\n"
        result += f"Check-in: {{checkin_date}} | Check-out: {{checkout_date}} | Guests: {{adults}}\\n\\n"

        for i, hotel in enumerate(top_hotels, 1):
            result += f"{{i}}. **{{hotel['name']}}** ({{hotel['type']}})\\n"
            result += f"   📍 Address: {{hotel['address']}}\\n"

            if hotel['phone']:
                result += f"   📞 Phone: {{hotel['phone']}}\\n"
            if hotel['website']:
                result += f"   🌐 Website: {{hotel['website']}}\\n"
            if hotel['email']:
                result += f"   ✉️ Email: {{hotel['email']}}\\n"
            if hotel['stars']:
                result += f"   ⭐ Stars: {{hotel['stars']}}\\n"

            result += f"   📏 Distance: {{hotel['distance']:.1f}} km from center\\n"
            result += f"   🗺️ Maps: https://www.google.com/maps?q={{hotel['lat']}},{{hotel['lon']}}\\n"
            result += f"   Source: OpenStreetMap\\n"
            result += f"   OSM URL: https://www.openstreetmap.org/?mlat={{hotel['lat']}}&mlon={{hotel['lon']}}#map=18/{{hotel['lat']}}/{{hotel['lon']}}\\n"
            result += f"   Data API: https://overpass-api.de/api/interpreter\\n\\n"


        result += f"\\n🔗 **BOOK THESE HOTELS:**\\n"
        result += f"• Booking.com: https://www.booking.com/searchresults.html?ss={{urllib.parse.quote(location)}}&checkin={{checkin_date}}&checkout={{checkout_date}}&group_adults={{adults}}\\n"
        result += f"• Hotels.com: https://www.hotels.com/search.do?destination={{urllib.parse.quote(location)}}&startDate={{checkin_date}}&endDate={{checkout_date}}\\n"
        result += f"• Airbnb: https://www.airbnb.com/s/{{urllib.parse.quote(location)}}/homes?checkin={{checkin_date}}&checkout={{checkout_date}}&adults={{adults}}\\n"

        return result

    except Exception as e:
        return f"Error searching hotels: {{str(e)}}"

@mcp.tool()
def get_top_hotels(location: str, limit: int = 5) -> str:
    """Get top hotel recommendations for a location using free public APIs

    Args:
        location: City or area name (e.g., "Mumbai", "Juhu Beach Mumbai")
        limit: Number of top hotels to return (default: 5, max: 15)
    """

    try:
        # Get coordinates for the location using a geocoding service
        geocode_url = f"https://nominatim.openstreetmap.org/search?format=json&q={{urllib.parse.quote(location)}}&limit=1"

        headers = {{
            'User-Agent': 'HotelSearchMCP/1.0'
        }}

        geocode_response = requests.get(geocode_url, headers=headers)
        geocode_response.raise_for_status()
        geocode_data = geocode_response.json()

        if not geocode_data:
            return f"Location '{{location}}' not found. Please try a more specific location name."

        lat = float(geocode_data[0]['lat'])
        lon = float(geocode_data[0]['lon'])
        display_name = geocode_data[0].get('display_name', location)

        # Search for accommodations using Overpass API (OpenStreetMap data)
        overpass_url = "https://overpass-api.de/api/interpreter"

        # Query for hotels within 15km radius, prioritizing higher-end accommodations
        overpass_query = f"""
        [out:json][timeout:30];
        (
          node["tourism"="hotel"]["stars"](around:15000,{{lat}},{{lon}});
          node["tourism"="hotel"]["name"](around:15000,{{lat}},{{lon}});
          way["tourism"="hotel"]["stars"](around:15000,{{lat}},{{lon}});
          way["tourism"="hotel"]["name"](around:15000,{{lat}},{{lon}});
        );
        out center meta;
        """

        overpass_response = requests.post(overpass_url, data={{'data': overpass_query}}, headers=headers)
        overpass_response.raise_for_status()
        overpass_data = overpass_response.json()

        hotels = overpass_data.get('elements', [])

        # Process and rank hotels
        processed_hotels = []
        for hotel in hotels:
            tags = hotel.get('tags', {{}})
            if 'name' in tags:
                # Get coordinates
                if hotel.get('type') == 'way' and 'center' in hotel:
                    hotel_lat = hotel['center']['lat']
                    hotel_lon = hotel['center']['lon']
                else:
                    hotel_lat = hotel.get('lat', lat)
                    hotel_lon = hotel.get('lon', lon)

                # Calculate distance
                distance = ((hotel_lat - lat) ** 2 + (hotel_lon - lon) ** 2) ** 0.5 * 111

                # Calculate ranking score (stars + inverse distance)
                stars = tags.get('stars', '0')
                try:
                    star_rating = float(stars) if stars else 0
                except:
                    star_rating = 0

                # Boost score for hotels with websites, phones, etc.
                quality_score = star_rating * 2
                if tags.get('website') or tags.get('contact:website'):
                    quality_score += 1
                if tags.get('phone') or tags.get('contact:phone'):
                    quality_score += 0.5
                if 'luxury' in tags.get('name', '').lower() or 'resort' in tags.get('name', '').lower():
                    quality_score += 1

                # Penalize distance (closer is better)
                final_score = quality_score - (distance / 10)

                processed_hotels.append({{
                    'name': tags.get('name', 'Unnamed Hotel'),
                    'stars': stars,
                    'address': tags.get('addr:full', tags.get('addr:street', tags.get('addr:city', 'Address not available'))),
                    'phone': tags.get('phone', tags.get('contact:phone', '')),
                    'website': tags.get('website', tags.get('contact:website', '')),
                    'email': tags.get('email', tags.get('contact:email', '')),
                    'distance': distance,
                    'score': final_score,
                    'lat': hotel_lat,
                    'lon': hotel_lon
                }})

        # Sort by score (best first) and limit results
        processed_hotels.sort(key=lambda x: x['score'], reverse=True)
        top_hotels = processed_hotels[:min(limit, 15)]

        if not top_hotels:
            # Fallback: provide curated recommendations
            result = f"Top hotel booking platforms for {{display_name}}:\\n\\n"
            result += f"🏨 **RECOMMENDED BOOKING SITES:**\\n"
            result += f"1. Booking.com - https://www.booking.com/searchresults.html?ss={{urllib.parse.quote(location)}}\\n"
            result += f"2. Hotels.com - https://www.hotels.com/search.do?destination={{urllib.parse.quote(location)}}\\n"
            result += f"3. Expedia - https://www.expedia.com/Hotel-Search?destination={{urllib.parse.quote(location)}}\\n"
            result += f"4. Agoda - https://www.agoda.com/search?city={{urllib.parse.quote(location)}}\\n"
            result += f"5. Trivago - https://www.trivago.com/{{urllib.parse.quote(location)}}\\n\\n"

            result += f"🏠 **ALTERNATIVE ACCOMMODATIONS:**\\n"
            result += f"• Airbnb - https://www.airbnb.com/s/{{urllib.parse.quote(location)}}\\n"
            result += f"• Vrbo - https://www.vrbo.com/search?destination={{urllib.parse.quote(location)}}\\n"
            result += f"• Hostelworld - https://www.hostelworld.com/search?search_keywords={{urllib.parse.quote(location)}}\\n\\n"

            result += f"💡 **TIPS:**\\n"
            result += f"• Compare prices across multiple platforms\\n"
            result += f"• Check cancellation policies\\n"
            result += f"• Read recent reviews\\n"
            result += f"• Book directly with hotel for potential perks\\n\\n"

            result += f"Source: Free recommendation service"
            return result

        result = f"🏆 **TOP {{len(top_hotels)}} HOTELS IN {{display_name.upper()}}**\\n\\n"

        for i, hotel in enumerate(top_hotels, 1):
            result += f"{{i}}. **{{hotel['name']}}**"
            if hotel['stars']:
                result += f" ⭐{{hotel['stars']}}-star"
            result += f"\\n"

            result += f"   📍 Address: {{hotel['address']}}\\n"

            if hotel['phone']:
                result += f"   📞 Phone: {{hotel['phone']}}\\n"
            if hotel['website']:
                result += f"   🌐 Website: {{hotel['website']}}\\n"
            if hotel['email']:
                result += f"   ✉️ Email: {{hotel['email']}}\\n"

            result += f"   📏 Distance: {{hotel['distance']:.1f}} km from center\\n"
            result += f"   🗺️ Maps: https://www.google.com/maps?q={{hotel['lat']}},{{hotel['lon']}}\\n"
            result += f"   Source: OpenStreetMap\\n"
            result += f"   OSM URL: https://www.openstreetmap.org/?mlat={{hotel['lat']}}&mlon={{hotel['lon']}}#map=18/{{hotel['lat']}}/{{hotel['lon']}}\\n"
            result += f"   Data API: https://overpass-api.de/api/interpreter\\n\\n"


        result += f"\\n🔗 **BOOK THESE HOTELS:**\\n"
        result += f"• Booking.com: https://www.booking.com/searchresults.html?ss={{urllib.parse.quote(location)}}\\n"
        result += f"• Hotels.com: https://www.hotels.com/search.do?destination={{urllib.parse.quote(location)}}\\n"
        result += f"• Expedia: https://www.expedia.com/Hotel-Search?destination={{urllib.parse.quote(location)}}\\n"
        result += f"• Airbnb: https://www.airbnb.com/s/{{urllib.parse.quote(location)}}\\n"

        return result

    except Exception as e:
        return f"Error getting top hotels: {{str(e)}}"



@mcp.tool()
def get_nearby_hotels(location: str, radius_km: int = 10) -> str:
    """Get hotels near a specific location or landmark using free public APIs

    Args:
        location: City, area, or landmark name (e.g., "Juhu Beach Mumbai", "Mumbai Airport")
        radius_km: Search radius in kilometers (default: 10, max: 25)
    """
    try:
        # Limit radius to reasonable bounds
        radius_km = min(max(radius_km, 1), 25)

        # Get coordinates for the location
        geocode_url = f"https://nominatim.openstreetmap.org/search?format=json&q={{urllib.parse.quote(location)}}&limit=1"

        headers = {{
            'User-Agent': 'HotelSearchMCP/1.0'
        }}

        geocode_response = requests.get(geocode_url, headers=headers)
        geocode_response.raise_for_status()
        geocode_data = geocode_response.json()

        if not geocode_data:
            return f"Location '{{location}}' not found. Please try a more specific location name."

        lat = float(geocode_data[0]['lat'])
        lon = float(geocode_data[0]['lon'])
        display_name = geocode_data[0].get('display_name', location)

        # Search for accommodations using Overpass API
        overpass_url = "https://overpass-api.de/api/interpreter"

        # Query for all types of accommodations within radius
        overpass_query = f"""
        [out:json][timeout:30];
        (
          node["tourism"="hotel"](around:{{radius_km * 1000}},{{lat}},{{lon}});
          node["tourism"="hostel"](around:{{radius_km * 1000}},{{lat}},{{lon}});
          node["tourism"="guest_house"](around:{{radius_km * 1000}},{{lat}},{{lon}});
          node["tourism"="motel"](around:{{radius_km * 1000}},{{lat}},{{lon}});
          way["tourism"="hotel"](around:{{radius_km * 1000}},{{lat}},{{lon}});
          way["tourism"="hostel"](around:{{radius_km * 1000}},{{lat}},{{lon}});
          way["tourism"="guest_house"](around:{{radius_km * 1000}},{{lat}},{{lon}});
          way["tourism"="motel"](around:{{radius_km * 1000}},{{lat}},{{lon}});
        );
        out center meta;
        """

        overpass_response = requests.post(overpass_url, data={{'data': overpass_query}}, headers=headers)
        overpass_response.raise_for_status()
        overpass_data = overpass_response.json()

        hotels = overpass_data.get('elements', [])

        # Process hotels
        processed_hotels = []
        for hotel in hotels:
            tags = hotel.get('tags', {{}})
            if 'name' in tags:
                # Get coordinates
                if hotel.get('type') == 'way' and 'center' in hotel:
                    hotel_lat = hotel['center']['lat']
                    hotel_lon = hotel['center']['lon']
                else:
                    hotel_lat = hotel.get('lat', lat)
                    hotel_lon = hotel.get('lon', lon)

                # Calculate distance
                distance = ((hotel_lat - lat) ** 2 + (hotel_lon - lon) ** 2) ** 0.5 * 111

                processed_hotels.append({{
                    'name': tags.get('name', 'Unnamed Hotel'),
                    'type': tags.get('tourism', 'hotel').replace('_', ' ').title(),
                    'address': tags.get('addr:full', tags.get('addr:street', tags.get('addr:city', 'Address not available'))),
                    'phone': tags.get('phone', tags.get('contact:phone', '')),
                    'website': tags.get('website', tags.get('contact:website', '')),
                    'email': tags.get('email', tags.get('contact:email', '')),
                    'stars': tags.get('stars', ''),
                    'distance': distance,
                    'lat': hotel_lat,
                    'lon': hotel_lon
                }})

        # Sort by distance
        processed_hotels.sort(key=lambda x: x['distance'])
        nearby_hotels = processed_hotels[:20]

        if not nearby_hotels:
            result = f"No hotels found within {{radius_km}}km of {{display_name}}.\\n\\n"
            result += f"🔍 **SEARCH ON BOOKING PLATFORMS:**\\n"
            result += f"• Booking.com: https://www.booking.com/searchresults.html?ss={{urllib.parse.quote(location)}}\\n"
            result += f"• Hotels.com: https://www.hotels.com/search.do?destination={{urllib.parse.quote(location)}}\\n"
            result += f"• Airbnb: https://www.airbnb.com/s/{{urllib.parse.quote(location)}}\\n"
            result += f"• Agoda: https://www.agoda.com/search?city={{urllib.parse.quote(location)}}\\n"
            result += f"\\nSource: Free OpenStreetMap data\\n"
            result += f"OSM URL: https://www.openstreetmap.org/search?query={{urllib.parse.quote(location)}}\\n"
            result += f"Data API: https://overpass-api.de/api/interpreter"

            return result


        result = f"🏨 **HOTELS NEAR {{display_name.upper()}}** (within {{radius_km}}km)\\n\\n"

        for i, hotel in enumerate(nearby_hotels, 1):
            result += f"{{i}}. **{{hotel['name']}}** ({{hotel['type']}})\\n"
            if hotel['stars']:
                result += f"   ⭐ {{hotel['stars']}}-star hotel\\n"
            result += f"   📍 {{hotel['address']}}\\n"
            result += f"   📏 {{hotel['distance']:.1f}} km away\\n"

            if hotel['phone']:
                result += f"   📞 {{hotel['phone']}}\\n"
            if hotel['website']:
                result += f"   🌐 {{hotel['website']}}\\n"
            if hotel['email']:
                result += f"   ✉️ {{hotel['email']}}\\n"

            result += f"   🗺️ https://www.google.com/maps?q={{hotel['lat']}},{{hotel['lon']}}\\n\\n"

        result += f"\\n  **BOOK THESE HOTELS:**\\n"
        result += f"• Booking.com: https://www.booking.com/searchresults.html?ss={{urllib.parse.quote(location)}}\\n"
        result += f"• Hotels.com: https://www.hotels.com/search.do?destination={{urllib.parse.quote(location)}}\\n"
        result += f"• Airbnb: https://www.airbnb.com/s/{{urllib.parse.quote(location)}}\\n"
        result += f"\\nSource: Free OpenStreetMap data\\n"
        result += f"OSM URL: https://www.openstreetmap.org/search?query={{urllib.parse.quote(location)}}\\n"
        result += f"Data API: https://overpass-api.de/api/interpreter"

        return result

    except Exception as e:
        return f"Error finding nearby hotels: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Hotels MCP server with stdio transport")
    mcp.run(transport="stdio")
'''
        elif service == "weather":
            api_key_value = credentials.get('api_key', '')
            return f'''#!/usr/bin/env python3
"""
Dynamic Weather MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Weather_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("weather_{server_id}", redirect_slashes=False, stateless_http=True)

# Weather Configuration
API_KEY = "{api_key_value}"

@mcp.tool()
def get_weather(location: str) -> str:
    """Get weather information for a location"""
    try:
        if not API_KEY:
            return "OpenWeatherMap API key is required. Sign up at https://openweathermap.org/ to get a free API key."

        url = f"http://api.openweathermap.org/data/2.5/weather?q={{location}}&appid={{API_KEY}}&units=metric"
        response = requests.get(url)
        response.raise_for_status()

        data = response.json()

        # Check for API errors
        if data.get('cod') != 200:
            error_msg = data.get('message', 'Unknown error')
            return f"Weather API Error: {{error_msg}}. Please check the location name and your API key."

        result = f"Weather in {{location}}:\\n"
        result += f"Temperature: {{data['main']['temp']}} deg C (feels like {{data['main']['feels_like']}} deg C)\\n"
        result += f"Condition: {{data['weather'][0]['description'].capitalize()}}\\n"
        result += f"Humidity: {{data['main']['humidity']}}%\\n"
        result += f"Pressure: {{data['main']['pressure']}} hPa\\n"
        result += f"Wind: {{data['wind']['speed']}} m/s\\n"
        result += f"Visibility: {{data.get('visibility', 'N/A')}} meters\\n"

        # Add specific source URLs like news does
        city_id = data.get('id', '')
        city_name = data.get('name', location)
        country_code = data.get('sys', {{}}).get('country', '')

        result += f"Source: OpenWeatherMap\\n"
        result += f"API URL: http://api.openweathermap.org/data/2.5/weather?q={{location}}&appid={{API_KEY}}&units=metric\\n"
        if city_id:
            result += f"City Details: https://openweathermap.org/city/{{city_id}}\\n"
        result += f"Weather Map: https://openweathermap.org/weathermap?basemap=map&cities=true&layer=temperature&lat={{data.get('coord', {{}}).get('lat', 0)}}&lon={{data.get('coord', {{}}).get('lon', 0)}}&zoom=10\\n"

        return result
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 401:
            return f"Invalid API key. Please check your OpenWeatherMap API key. Error: {{str(e)}}"
        elif e.response.status_code == 404:
            return f"Location '{{location}}' not found. Please check the spelling and try again. Error: {{str(e)}}"
        else:
            return f"HTTP Error getting weather: {{str(e)}}"
    except Exception as e:
        return f"Error getting weather: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Weather MCP server with stdio transport")
    mcp.run(transport="stdio")

'''
        elif service == "news":
            api_key_value = credentials.get('api-key', credentials.get('api_key', ''))
            return f'''#!/usr/bin/env python3
"""
Dynamic News MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("News_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("news_{server_id}", redirect_slashes=False, stateless_http=True)

# News Configuration
API_KEY = "{api_key_value}"

@mcp.tool()
def get_latest_news(category: str = "general") -> str:
    """Get latest news"""
    try:
        if not API_KEY:
            return "NewsAPI key is required. Sign up at https://newsapi.org/."

        valid_categories = ['business', 'entertainment', 'general', 'health', 'science', 'sports', 'technology']
        if category not in valid_categories:
            category = 'general'

        url = f"https://newsapi.org/v2/top-headlines?category={{category}}&apiKey={{API_KEY}}&pageSize=10"
        response = requests.get(url)
        response.raise_for_status()

        articles = response.json().get('articles', [])

        if not articles:
            return f"No news found for category '{{category}}'"

        result = f"Latest {{category}} news:\\n"
        for i, article in enumerate(articles, 1):
            result += f"{{i}}. {{article.get('title', 'No title')}}\\n"
            result += f"   Source: {{article.get('source', {{}}).get('name', 'Unknown')}}\\n"
            result += f"   URL: {{article.get('url', 'N/A')}}\\n\\n"

        return result
    except Exception as e:
        return f"Error getting news: {{str(e)}}"

@mcp.tool()
def search_news(query: str, language: str = "en") -> str:
    """Search for news articles by query"""
    try:
        if not API_KEY:
            return "NewsAPI key is required. Sign up at https://newsapi.org/."

        url = f"https://newsapi.org/v2/everything?q={{query}}&language={{language}}&apiKey={{API_KEY}}&pageSize=10&sortBy=publishedAt"
        response = requests.get(url)
        response.raise_for_status()

        articles = response.json().get('articles', [])

        if not articles:
            return f"No news found for query '{{query}}'"

        result = f"News search results for '{{query}}':\\n"
        for i, article in enumerate(articles, 1):
            result += f"{{i}}. {{article.get('title', 'No title')}}\\n"
            result += f"   Source: {{article.get('source', {{}}).get('name', 'Unknown')}}\\n"
            result += f"   Published: {{article.get('publishedAt', 'Unknown')}}\\n"
            result += f"   URL: {{article.get('url', 'N/A')}}\\n\\n"

        return result
    except Exception as e:
        return f"Error searching news: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting News MCP server with stdio transport")
    mcp.run(transport="stdio")
'''
        elif service == "google":
            api_key_value = credentials.get('api_key', '')
            cx_value = credentials.get('cx', '')
            return f'''#!/usr/bin/env python3
"""
Dynamic Google MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Google_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("google_{server_id}", redirect_slashes=False, stateless_http=True)

# Google Configuration
API_KEY = "{api_key_value}"
CX = "{cx_value}"

@mcp.tool()
def google_search(query: str) -> str:
    """Perform Google search"""
    try:
        if not API_KEY:
            return "Google API key is required. Please provide your Google Custom Search API key. Sign up at https://console.cloud.google.com/ to get an API key."

        if not CX:
            return "Google Custom Search Engine ID (cx) is required. Please provide your Custom Search Engine ID. Create one at https://programmablesearchengine.google.com/."

        url = f"https://www.googleapis.com/customsearch/v1?key={{API_KEY}}&cx={{CX}}&q={{query}}&num=10"
        response = requests.get(url)
        response.raise_for_status()

        data = response.json()

        # Check for API errors
        if 'error' in data:
            error_msg = data['error'].get('message', 'Unknown API error')
            return f"Google API Error: {{error_msg}}. Please check your API key and Custom Search Engine ID."

        results = data.get('items', [])

        if not results:
            return f"No search results found for '{{query}}'. This might be due to API quota limits or search restrictions."

        result = f"Google search results for '{{query}}':\\n"
        for i, item in enumerate(results, 1):
            title = item.get('title', 'No title')
            link = item.get('link', 'N/A')
            snippet = item.get('snippet', 'No snippet')

            result += f"{{i}}. {{title}}\\n"
            result += f"   URL: {{link}}\\n"
            result += f"   Snippet: {{snippet[:150]}}...\\n\\n"

        return result
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 403:
            return f"Google API access forbidden. Please check your API key permissions and billing status. Error: {{str(e)}}"
        elif e.response.status_code == 429:
            return f"Google API quota exceeded. Please check your usage limits. Error: {{str(e)}}"
        else:
            return f"HTTP Error performing Google search: {{str(e)}}"
    except Exception as e:
        return f"Error performing Google search: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Google MCP server with stdio transport")
    mcp.run(transport="stdio")
'''
        elif service == "brave_search":
            api_key_value = credentials.get('api_key', '')
            return f'''#!/usr/bin/env python3
"""
Dynamic Brave Search MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("BraveSearch_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("brave_search_{server_id}", redirect_slashes=False, stateless_http=True)

# Brave Search Configuration
API_KEY = "{api_key_value}"

@mcp.tool()
def brave_search(query: str, count: int = 10) -> str:
    """Perform Brave search using Brave Search API

    Args:
        query: Search query string
        count: Number of results to return (default: 10, max: 20)
    """
    try:
        if not API_KEY:
            return "Brave Search API key is required. Sign up at https://api.search.brave.com/ to get a free API key."

        # Limit count to reasonable bounds
        count = min(max(count, 1), 20)

        url = "https://api.search.brave.com/res/v1/web/search"
        headers = {{
            "Accept": "application/json",
            "Accept-Encoding": "gzip",
            "X-Subscription-Token": API_KEY
        }}

        params = {{
            "q": query,
            "count": count,
            "offset": 0,
            "mkt": "en-US",
            "safesearch": "moderate",
            "freshness": "",
            "text_decorations": False,
            "spellcheck": True
        }}

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()

        # Check for API errors
        if 'error' in data:
            error_msg = data['error'].get('message', 'Unknown API error')
            return f"Brave Search API Error: {{error_msg}}. Please check your API key."

        web_results = data.get('web', {{}}).get('results', [])

        if not web_results:
            return f"No search results found for '{{query}}'. Try different keywords."

        result = f"Brave search results for '{{query}}':\\n\\n"

        for i, item in enumerate(web_results, 1):
            title = item.get('title', 'No title')
            url_link = item.get('url', 'N/A')
            description = item.get('description', 'No description')

            result += f"{{i}}. {{title}}\\n"
            result += f"   URL: {{url_link}}\\n"
            result += f"   Description: {{description[:200]}}...\\n"
            result += f"   Source: Brave Search\\n\\n"

        # Add source URLs
        result += f"\\nSource: Brave Search API\\n"
        result += f"API URL: https://api.search.brave.com/res/v1/web/search\\n"
        result += f"Documentation: https://api.search.brave.com/app/documentation/web-search/get-started"

        return result

    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 401:
            return f"Invalid Brave Search API key. Please check your subscription token. Error: {{str(e)}}"
        elif e.response.status_code == 429:
            return f"Brave Search API rate limit exceeded. Please wait before making more requests. Error: {{str(e)}}"
        elif e.response.status_code == 403:
            return f"Brave Search API access forbidden. Please check your subscription status. Error: {{str(e)}}"
        else:
            return f"HTTP Error performing Brave search: {{str(e)}}"
    except Exception as e:
        return f"Error performing Brave search: {{str(e)}}"

@mcp.tool()
def brave_news_search(query: str, count: int = 10) -> str:
    """Search for news using Brave Search API

    Args:
        query: News search query
        count: Number of news results to return (default: 10, max: 20)
    """
    try:
        if not API_KEY:
            return "Brave Search API key is required. Sign up at https://api.search.brave.com/ to get a free API key."

        count = min(max(count, 1), 20)

        url = "https://api.search.brave.com/res/v1/news/search"
        headers = {{
            "Accept": "application/json",
            "Accept-Encoding": "gzip",
            "X-Subscription-Token": API_KEY
        }}

        params = {{
            "q": query,
            "count": count,
            "offset": 0,
            "mkt": "en-US",
            "safesearch": "moderate",
            "freshness": "pd",  # Past day
            "text_decorations": False
        }}

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()

        news_results = data.get('results', [])

        if not news_results:
            return f"No news results found for '{{query}}'. Try different keywords."

        result = f"Brave news search results for '{{query}}':\\n\\n"

        for i, item in enumerate(news_results, 1):
            title = item.get('title', 'No title')
            url_link = item.get('url', 'N/A')
            description = item.get('description', 'No description')
            age = item.get('age', 'Unknown time')

            result += f"{{i}}. {{title}}\\n"
            result += f"   URL: {{url_link}}\\n"
            result += f"   Description: {{description[:200]}}...\\n"
            result += f"   Published: {{age}}\\n"
            result += f"   Source: Brave News Search\\n\\n"

        result += f"\\nSource: Brave Search News API\\n"
        result += f"API URL: https://api.search.brave.com/res/v1/news/search\\n"
        result += f"Documentation: https://api.search.brave.com/app/documentation/news-search/get-started"

        return result

    except Exception as e:
        return f"Error performing Brave news search: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Brave Search MCP server with stdio transport")
    mcp.run(transport="stdio")
'''
        elif service == "google_maps":
            api_key_value = credentials.get('api_key', '')
            return f'''#!/usr/bin/env python3
"""
Dynamic Google Maps MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP
import urllib.parse
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GoogleMaps_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("google_maps_{server_id}", redirect_slashes=False, stateless_http=True)

# Google Maps Configuration
API_KEY = "{api_key_value}"

@mcp.tool()
def search_places(query: str, location: str = "", radius: int = 5000) -> str:
    """Search for places using Google Maps Places API

    Args:
        query: Search query (e.g., "restaurants", "hotels", "gas stations")
        location: Location to search around (e.g., "New York, NY")
        radius: Search radius in meters (default: 5000, max: 50000)
    """
    try:
        if not API_KEY:
            return "Google Maps API key is required. Get one at https://console.cloud.google.com/ and enable Places API."

        # Limit radius to reasonable bounds
        radius = min(max(radius, 100), 50000)

        # If location is provided, get coordinates first
        if location:
            geocode_url = "https://maps.googleapis.com/maps/api/geocode/json"
            geocode_params = {{
                "address": location,
                "key": API_KEY
            }}

            geocode_response = requests.get(geocode_url, params=geocode_params)
            geocode_response.raise_for_status()
            geocode_data = geocode_response.json()

            if geocode_data.get('status') != 'OK':
                return f"Could not find location '{{location}}'. Please try a more specific address."

            location_coords = geocode_data['results'][0]['geometry']['location']
            lat = location_coords['lat']
            lng = location_coords['lng']
            location_param = f"{{lat}},{{lng}}"
        else:
            location_param = ""

        # Search for places
        places_url = "https://maps.googleapis.com/maps/api/place/textsearch/json"
        places_params = {{
            "query": query,
            "key": API_KEY,
            "type": "establishment"
        }}

        if location_param:
            places_params["location"] = location_param
            places_params["radius"] = radius

        places_response = requests.get(places_url, params=places_params)
        places_response.raise_for_status()
        places_data = places_response.json()

        if places_data.get('status') != 'OK':
            error_msg = places_data.get('error_message', 'Unknown API error')
            return f"Google Maps API Error: {{error_msg}}. Status: {{places_data.get('status')}}"

        results = places_data.get('results', [])

        if not results:
            return f"No places found for '{{query}}' near {{location or 'your location'}}."

        result = f"Google Maps search results for '{{query}}'{{' near ' + location if location else ''}}:\\n\\n"

        for i, place in enumerate(results[:15], 1):
            name = place.get('name', 'Unknown Place')
            address = place.get('formatted_address', 'Address not available')
            rating = place.get('rating', 'No rating')
            price_level = place.get('price_level', None)
            place_id = place.get('place_id', '')

            # Get place types
            types = place.get('types', [])
            place_types = ', '.join([t.replace('_', ' ').title() for t in types[:3]])

            result += f"{{i}}. **{{name}}**\\n"
            result += f"   📍 Address: {{address}}\\n"
            result += f"   ⭐ Rating: {{rating}}/5\\n"

            if price_level is not None:
                price_symbols = '$' * (price_level + 1)
                result += f"   💰 Price Level: {{price_symbols}} ({{price_level + 1}}/4)\\n"

            if place_types:
                result += f"   🏷️ Type: {{place_types}}\\n"

            if place_id:
                result += f"   🗺️ Google Maps: https://www.google.com/maps/place/?q=place_id:{{place_id}}\\n"

            result += f"   Source: Google Maps Places API\\n"
            result += f"   API URL: https://maps.googleapis.com/maps/api/place/textsearch/json\\n\\n"

        result += f"\\nSource: Google Maps Places API\\n"
        result += f"Documentation: https://developers.google.com/maps/documentation/places/web-service"

        return result

    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 403:
            return f"Google Maps API access forbidden. Check your API key and billing. Error: {{str(e)}}"
        elif e.response.status_code == 429:
            return f"Google Maps API quota exceeded. Check your usage limits. Error: {{str(e)}}"
        else:
            return f"HTTP Error searching places: {{str(e)}}"
    except Exception as e:
        return f"Error searching places: {{str(e)}}"

@mcp.tool()
def get_directions(origin: str, destination: str, mode: str = "driving") -> str:
    """Get directions between two locations using Google Maps Directions API

    Args:
        origin: Starting location (address or coordinates)
        destination: Destination location (address or coordinates)
        mode: Travel mode (driving, walking, bicycling, transit)
    """
    try:
        if not API_KEY:
            return "Google Maps API key is required. Get one at https://console.cloud.google.com/ and enable Directions API."

        valid_modes = ["driving", "walking", "bicycling", "transit"]
        if mode not in valid_modes:
            mode = "driving"

        directions_url = "https://maps.googleapis.com/maps/api/directions/json"
        directions_params = {{
            "origin": origin,
            "destination": destination,
            "mode": mode,
            "key": API_KEY,
            "units": "metric"
        }}

        directions_response = requests.get(directions_url, params=directions_params)
        directions_response.raise_for_status()
        directions_data = directions_response.json()

        if directions_data.get('status') != 'OK':
            error_msg = directions_data.get('error_message', 'Unknown API error')
            return f"Google Maps Directions API Error: {{error_msg}}. Status: {{directions_data.get('status')}}"

        routes = directions_data.get('routes', [])

        if not routes:
            return f"No routes found from '{{origin}}' to '{{destination}}' via {{mode}}."

        route = routes[0]  # Get the first (best) route
        leg = route['legs'][0]  # Get the first leg

        result = f"Directions from {{origin}} to {{destination}} ({{mode.title()}}):\\n\\n"

        # Route summary
        distance = leg.get('distance', {{}}).get('text', 'Unknown distance')
        duration = leg.get('duration', {{}}).get('text', 'Unknown duration')

        result += f"📏 **Distance:** {{distance}}\\n"
        result += f"⏱️ **Duration:** {{duration}}\\n"
        result += f"🚗 **Mode:** {{mode.title()}}\\n\\n"

        # Step-by-step directions
        steps = leg.get('steps', [])
        result += f"**Step-by-step directions:**\\n"

        for i, step in enumerate(steps, 1):
            instruction = step.get('html_instructions', 'No instruction')
            # Remove HTML tags
            instruction = re.sub('<.*?>', '', instruction)

            step_distance = step.get('distance', {{}}).get('text', '')
            step_duration = step.get('duration', {{}}).get('text', '')

            result += f"{{i}}. {{instruction}}"
            if step_distance:
                result += f" ({{step_distance}}, {{step_duration}})"
            result += f"\\n"

        # Google Maps link
        maps_url = f"https://www.google.com/maps/dir/{{urllib.parse.quote(origin)}}/{{urllib.parse.quote(destination)}}"
        result += f"\\n🗺️ **View on Google Maps:** {{maps_url}}\\n"

        result += f"\\nSource: Google Maps Directions API\\n"
        result += f"API URL: https://maps.googleapis.com/maps/api/directions/json\\n"
        result += f"Documentation: https://developers.google.com/maps/documentation/directions"

        return result

    except Exception as e:
        return f"Error getting directions: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Google Maps MCP server with stdio transport")
    mcp.run(transport="stdio")
'''
        else:
            raise ValueError(f"Unsupported public service: {{service}}")
    

    async def start_server(self, server_id: str) -> bool:
        """Start a dynamic MCP server"""
        try:
            if server_id not in self.servers:
                logger.error(f"Server {server_id} not found")
                return False

            config = self.servers[server_id]
            if config.get("running", False):
                logger.info(f"Server {server_id} is already running")
                return True

            # Start the server process
            server_file = self.server_dir / server_id / f"{server_id}.py"
            if not server_file.exists():
                logger.error(f"Server file not found: {server_file}")
                return False

            # Start server process
            process = subprocess.Popen([
                "python", str(server_file)
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # Store process
            self.processes[server_id] = process

            # Update server status
            config["status"] = "running"
            config["running"] = True
            config["last_used"] = datetime.now()

            logger.info(f"Started dynamic MCP server: {server_id}")
            return True

        except Exception as e:
            logger.error(f"Error starting server {server_id}: {e}")
            return False

    async def stop_server(self, server_id: str) -> bool:
        """Stop a dynamic MCP server"""
        try:
            if server_id not in self.servers:
                logger.error(f"Server {server_id} not found")
                return False

            # Stop process if running
            if server_id in self.processes:
                process = self.processes[server_id]
                process.terminate()
                process.wait(timeout=5)
                del self.processes[server_id]

            # Update server status
            config = self.servers[server_id]
            config["status"] = "stopped"
            config["running"] = False

            logger.info(f"Stopped dynamic MCP server: {server_id}")
            return True

        except Exception as e:
            logger.error(f"Error stopping server {server_id}: {e}")
            return False

    async def delete_server(self, server_id: str) -> bool:
        """Delete a dynamic MCP server"""
        try:
            # Stop server first
            await self.stop_server(server_id)

            # Remove server directory
            server_path = self.server_dir / server_id
            if server_path.exists():
                shutil.rmtree(server_path)

            # Remove from servers dict
            if server_id in self.servers:
                del self.servers[server_id]

            logger.info(f"Deleted dynamic MCP server: {server_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting server {server_id}: {e}")
            return False

    def list_servers(self) -> List[Dict]:
        """List all dynamic MCP servers"""
        return list(self.servers.values())

    def get_server_info(self, server_id: str) -> Optional[Dict]:
        """Get information about a specific server"""
        return self.servers.get(server_id)

    def get_server_url(self, server_id: str) -> Optional[str]:
        """Get the URL of a specific server"""
        if server_id in self.servers:
            return self.servers[server_id].get("url")
        return None

# Global factory instance
dynamic_mcp_factory = DynamicMCPFactory()

# Factory functions for external use
async def create_dynamic_mcp_server(server_type: str, category: str, service: str, credentials: Dict[str, Any],
                                  server_name: str, description: str = "", user_id: Optional[str] = None) -> Dict:
    """Create a new dynamic MCP server"""
    return await dynamic_mcp_factory.create_server(
        server_type, category, service, credentials, server_name, description, user_id
    )

async def start_dynamic_mcp_server(server_id: str) -> bool:
    """Start a dynamic MCP server"""
    return await dynamic_mcp_factory.start_server(server_id)

async def stop_dynamic_mcp_server(server_id: str) -> bool:
    """Stop a dynamic MCP server"""
    return await dynamic_mcp_factory.stop_server(server_id)

async def delete_dynamic_mcp_server(server_id: str) -> bool:
    """Delete a dynamic MCP server"""
    return await dynamic_mcp_factory.delete_server(server_id)

def list_dynamic_mcp_servers() -> List[Dict]:
    """List all dynamic MCP servers"""
    return dynamic_mcp_factory.list_servers()

def get_dynamic_mcp_server_info(server_id: str) -> Optional[Dict]:
    """Get information about a specific server"""
    return dynamic_mcp_factory.get_server_info(server_id)

def get_dynamic_mcp_server_url(server_id: str) -> Optional[str]:
    """Get the URL of a specific server"""
    return dynamic_mcp_factory.get_server_url(server_id)