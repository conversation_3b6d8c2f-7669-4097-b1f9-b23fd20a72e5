qdrant-client
fastapi
uvicorn
python-dotenv
pydantic
pymongo
requests
packaging
httpx

# MCP (Model Context Protocol)
mcp
mcp[cli]
fastmcp

redis
redis[hiredis]

# Security and Guardrails
guardrails-ai
presidio-analyzer
# SSL/TLS Support for Redis
cryptography


# Logging and Utilities
typing-extensions

# Machine Learning and NLP
sentence-transformers
transformers
torch
numpy
pandas

# LangChain and AI Framework
langchain-mcp-adapters
langchain-openai
langchain
langchain-groq
langchain-huggingface
langchain_ollama
langgraph

# Document Processing
PyPDF2 
python-docx 
openpyxl      
pillow
pdf2image

python-multipart
python-magic
aiohttp

# OCR (Optional - for document processing)
pytesseract

# Cloud Storage Dependencies
boto3
google-cloud-storage
azure-storage-blob

# Google Drive API Dependencies
google-api-python-client
google-auth
google-auth-httplib2
google-auth-oauthlib

# Database Dependencies
psycopg2-binary
mysql-connector-python
pyodbc

# Microsoft Graph API (for OneDrive/SharePoint)
msal
msgraph-core
