#!/usr/bin/env python3
"""
Simple test to verify the follow-up question fix
"""
import requests
import json

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_ID = "64f1a9c8b2e94f3a7c1d2e45"

def test_query():
    """Test a simple query to see follow-up behavior"""
    
    print("🧪 Testing Follow-up Question Fix")
    print("=" * 50)
    
    # Step 1: Create a new chat
    print("1. Creating new chat...")
    try:
        new_chat_response = requests.post(f"{BASE_URL}/new_chat")
        if new_chat_response.status_code != 200:
            print(f"❌ Failed to create new chat: {new_chat_response.status_code}")
            print(f"Response: {new_chat_response.text}")
            return False
        
        chat_id = new_chat_response.json()["chat_id"]
        print(f"✅ Created chat: {chat_id}")
    except Exception as e:
        print(f"❌ Error creating chat: {e}")
        return False
    
    # Step 2: Send a query
    print("\n2. Sending test query...")
    query_data = {
        "query": "<PERSON>ira<PERSON>",
        "chat_id": chat_id,
        "user_id": TEST_USER_ID,
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.1-8b-instant",
        "api_key": "gsk_your_api_key_here",  # You'll need to replace this
        "collections": ["Gen AI"]
    }
    
    try:
        query_response = requests.post(f"{BASE_URL}/query", json=query_data)
        print(f"Query response status: {query_response.status_code}")
        
        if query_response.status_code != 200:
            print(f"❌ Query failed: {query_response.status_code}")
            print(f"Response: {query_response.text}")
            return False
        
        response_data = query_response.json()
        print(f"✅ Query successful")
        
        # Print the answer to see if follow-up is included
        answer = response_data['answer']
        print(f"\n📝 Answer (first 200 chars): {answer[:200]}...")
        
        if "👉" in answer:
            follow_up_start = answer.find("👉")
            displayed_follow_up = answer[follow_up_start:].strip()
            print(f"📝 Follow-up in response: {displayed_follow_up}")
        else:
            print("⚠️ No follow-up question found in response")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during query: {e}")
        return False

if __name__ == "__main__":
    test_query()
