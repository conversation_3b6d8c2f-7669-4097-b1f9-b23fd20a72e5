from memory_manager import get_memory_manager

# Example placeholder setup for Redis and MongoDB clients
# Replace these with your actual connection code
redis_client = None  # TODO: Replace with real Redis client
mongo_client = None  # TODO: Replace with real MongoDB client
mongo_db = None      # TODO: Replace with real database
history_collection = None  # TODO: Replace with real collection

# Initialize the memory manager instance
memory_manager = get_memory_manager(
    redis_client=redis_client,
    mongo_client=mongo_client,
    mongo_db=mongo_db,
    mongo_collection=history_collection
)

def main():
    # Example usage of the instance-based memory manager
    user_id = "user123"
    chat_id = "chat456"
    message = {"role": "user", "content": "Hello!", "timestamp": "2025-07-19T02:10:00Z"}
    memory_manager.store_short_term_message(user_id, chat_id, message)
    print("Message stored in Redis (if configured)!")

if __name__ == "__main__":
    main()
