#!/usr/bin/env python3
"""
Test script to verify the follow-up question fix
"""
import requests
import json
import time

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_ID = "64f1a9c8b2e94f3a7c1d2e45"

def test_follow_up_consistency():
    """Test that the same follow-up question is stored in Redis and displayed in FastAPI"""
    
    print("🧪 Testing Follow-up Question Consistency")
    print("=" * 50)
    
    # Step 1: Create a new chat
    print("1. Creating new chat...")
    new_chat_response = requests.post(f"{BASE_URL}/new_chat")
    if new_chat_response.status_code != 200:
        print(f"❌ Failed to create new chat: {new_chat_response.status_code}")
        return False
    
    chat_id = new_chat_response.json()["chat_id"]
    print(f"✅ Created chat: {chat_id}")
    
    # Step 2: Send a query that should generate a follow-up question
    print("\n2. Sending test query...")
    query_data = {
        "query": "<PERSON><PERSON><PERSON>",
        "chat_id": chat_id,
        "user_id": TEST_USER_ID,
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.1-8b-instant",
        "api_key": "your_groq_api_key_here",  # Replace with actual API key
        "collections": ["Gen AI"]
    }
    
    query_response = requests.post(f"{BASE_URL}/query", json=query_data)
    if query_response.status_code != 200:
        print(f"❌ Query failed: {query_response.status_code}")
        print(f"Response: {query_response.text}")
        return False
    
    response_data = query_response.json()
    print(f"✅ Query successful")
    print(f"Answer length: {len(response_data['answer'])} characters")
    
    # Step 3: Check if follow-up question is in the response
    answer = response_data['answer']
    if "👉" in answer:
        # Extract the follow-up question from the displayed answer
        follow_up_start = answer.find("👉")
        displayed_follow_up = answer[follow_up_start:].strip()
        print(f"📝 Follow-up in response: {displayed_follow_up}")
    else:
        print("⚠️ No follow-up question found in response")
        return False
    
    # Step 4: Get chat history to see what's stored
    print("\n3. Checking chat history...")
    history_response = requests.get(f"{BASE_URL}/history/{chat_id}")
    if history_response.status_code != 200:
        print(f"❌ Failed to get history: {history_response.status_code}")
        return False
    
    history_data = history_response.json()
    messages = history_data.get('messages', [])
    
    # Find the assistant message
    assistant_message = None
    for msg in messages:
        if msg.get('role') == 'assistant':
            assistant_message = msg
            break
    
    if not assistant_message:
        print("❌ No assistant message found in history")
        return False
    
    # Check the follow-up in metadata
    stored_follow_up = assistant_message.get('metadata', {}).get('follow_up')
    if stored_follow_up:
        print(f"💾 Follow-up in Redis: 👉 {stored_follow_up}")
    else:
        print("⚠️ No follow-up found in Redis metadata")
        return False
    
    # Step 5: Compare the follow-up questions
    print("\n4. Comparing follow-up questions...")
    
    # Extract just the question text from the displayed follow-up
    displayed_question = displayed_follow_up.replace("👉", "").strip()
    
    if displayed_question == stored_follow_up:
        print("✅ SUCCESS: Follow-up questions match!")
        print(f"   Both contain: '{stored_follow_up}'")
        return True
    else:
        print("❌ FAILURE: Follow-up questions don't match!")
        print(f"   Displayed: '{displayed_question}'")
        print(f"   Stored:    '{stored_follow_up}'")
        return False

def test_follow_up_continuation():
    """Test that follow-up continuation works correctly"""
    
    print("\n🔄 Testing Follow-up Continuation")
    print("=" * 50)
    
    # Step 1: Create a new chat
    print("1. Creating new chat...")
    new_chat_response = requests.post(f"{BASE_URL}/new_chat")
    if new_chat_response.status_code != 200:
        print(f"❌ Failed to create new chat: {new_chat_response.status_code}")
        return False
    
    chat_id = new_chat_response.json()["chat_id"]
    print(f"✅ Created chat: {chat_id}")
    
    # Step 2: Send initial query
    print("\n2. Sending initial query...")
    query_data = {
        "query": "Virat Kohli",
        "chat_id": chat_id,
        "user_id": TEST_USER_ID,
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.1-8b-instant",
        "api_key": "your_groq_api_key_here",  # Replace with actual API key
        "collections": ["Gen AI"]
    }
    
    query_response = requests.post(f"{BASE_URL}/query", json=query_data)
    if query_response.status_code != 200:
        print(f"❌ Initial query failed: {query_response.status_code}")
        return False
    
    print("✅ Initial query successful")
    
    # Step 3: Send affirmative response to trigger follow-up
    print("\n3. Sending affirmative response...")
    followup_data = {
        "query": "yes",
        "chat_id": chat_id,
        "user_id": TEST_USER_ID,
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.1-8b-instant",
        "api_key": "your_groq_api_key_here",  # Replace with actual API key
        "collections": ["Gen AI"]
    }
    
    followup_response = requests.post(f"{BASE_URL}/query", json=followup_data)
    if followup_response.status_code != 200:
        print(f"❌ Follow-up query failed: {followup_response.status_code}")
        return False
    
    followup_data = followup_response.json()
    print("✅ Follow-up continuation successful")
    print(f"Follow-up answer length: {len(followup_data['answer'])} characters")
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Follow-up Question Fix Tests")
    print("=" * 60)
    
    # Test 1: Follow-up consistency
    test1_result = test_follow_up_consistency()
    
    # Test 2: Follow-up continuation
    test2_result = test_follow_up_continuation()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    print(f"Follow-up Consistency: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Follow-up Continuation: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 ALL TESTS PASSED! The follow-up fix is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
