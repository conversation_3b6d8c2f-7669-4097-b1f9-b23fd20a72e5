# Use official Python base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install curl (for healthcheck)
RUN apt-get update && \
    apt-get install -y curl && \
    apt-get clean

# Install libgomp1 (required by transformers, etc.)
RUN apt-get update && \
    apt-get install -y libgomp1 && \
    apt-get clean

# Install git (for cloning repos if needed)
# Install Git first
RUN apt-get update && \
    apt-get install -y git && \
    apt-get clean

# Install SSH client (for Redis tunneling)
RUN apt-get update && \
    apt-get install -y openssh-client && \
    apt-get clean

# Add this to force CPU-only PyTorch wheels
ENV PIP_EXTRA_INDEX_URL=https://download.pytorch.org/whl/cpu

# Copy requirements file
COPY requirements.txt .

# Upgrade pip and install Python dependencies
RUN pip install --upgrade pip==25.1.1
RUN pip install --no-cache-dir -r requirements.txt

# Copy full application code
COPY . .

# Expose FastAPI port
EXPOSE 8001

# Command to run the FastAPI app
CMD ["uvicorn", "main1:app", "--host", "0.0.0.0", "--port", "8001"]