#!/usr/bin/env python3
"""
Qdrant MCP Agent with stdio transport
No server URL needed - connects directly to server process
Run this: python qdrant_agent.py
"""
import asyncio
import logging
import os
import subprocess
from pathlib import Path
from dotenv import load_dotenv
from mcp import ClientSession, StdioServerParameters, stdio_client

# Load environment variables
load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("QdrantAgent")

COLLECTION_NAME = os.getenv("COLLECTION_NAME", "Gen AI")

async def _direct_qdrant_search(query: str, collection_name: str = None) -> str:
    """Direct Qdrant API search as fallback when MCP fails"""
    try:
        import httpx
        from sentence_transformers import SentenceTransformer

        if collection_name is None:
            collection_name = COLLECTION_NAME

        # Load model
        model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")

        # Get environment variables
        qdrant_url = os.getenv("QDRANT_URL")
        qdrant_api_key = os.getenv("QDRANT_API_KEY")

        if not qdrant_url or not qdrant_api_key:
            return "❌ Qdrant configuration missing"

        # Create embedding
        embedding = model.encode(query).tolist()

        # Search payload
        payload = {
            "vector": embedding,
            "limit": 6,
            "with_payload": True,
            "score_threshold": 0.05 
        }

        headers = {
            "Authorization": f"Bearer {qdrant_api_key}",
            "Content-Type": "application/json"
        }

        url = f"{qdrant_url}/collections/{collection_name}/points/search"

        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()
            results = response.json().get("result", [])

            if not results:
                return "No relevant information found in Qdrant."

            formatted_results = []
            for item in results:
                text = item["payload"].get("content", "").strip()
                score = item.get("score", 0.0)
                source_file = item["payload"].get("filename", "unknown_file")

                # Format for aggregator agent
                formatted_results.append(
                    f"{text} [Score: {score:.3f}] Source: {source_file} | Collection: {collection_name}"
                )

            return "\n".join(formatted_results)

    except Exception as e:
        logger.error(f"Direct Qdrant search failed: {e}")
        return f"❌ Direct search error: {str(e)}"

async def query_qdrant_tool(query: str, collection_name: str = None) -> str:
    """Query the Qdrant tool - try direct API first, then MCP as fallback."""
    if collection_name is None:
        collection_name = COLLECTION_NAME

    logger.info(f"🔍 Searching Qdrant for: {query}")

    # Try direct API first (faster and more reliable)
    try:
        logger.info("🔄 Attempting direct Qdrant API...")
        result = await _direct_qdrant_search(query, collection_name)
        if result and not result.startswith("❌"):
            return result
    except Exception as direct_error:
        logger.warning(f"Direct API failed: {direct_error}")

    # Fallback to MCP if direct API fails
    try:
        logger.info("🔄 Falling back to MCP stdio transport...")
        # Add timeout wrapper for the entire operation
        async def _query_with_timeout():
            # Get the server script path
            server_script = Path(__file__).parent / "qdrant.py"

            if not server_script.exists():
                return f"❌ Qdrant server script not found: {server_script}"

            # Create stdio client session
            server_params = StdioServerParameters(
                command="python",
                args=[str(server_script)]
            )

            # Connect using stdio client
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    # Initialize the session with timeout
                    await asyncio.wait_for(session.initialize(), timeout=5.0)

                    # List available tools to verify connection
                    tools_result = await asyncio.wait_for(session.list_tools(), timeout=3.0)
                    tools = tools_result.tools if tools_result else []
                    logger.info(f"Connected to Qdrant MCP server with {len(tools)} tools")

                    # Call the qdrant_find tool with timeout
                    result = await asyncio.wait_for(
                        session.call_tool("qdrant_find", {
                            "query": query,
                            "collection_name": collection_name
                        }),
                        timeout=10.0
                    )

                    logger.info(f"Tool call successful")

                    # Handle the result
                    if not result or not result.content:
                        return "No relevant information found in Qdrant."

                    # Format the results for display
                    formatted_results = []

                    for content_item in result.content:
                        if hasattr(content_item, 'text'):
                            text_content = content_item.text
                            try:
                                # Try to parse as JSON if it contains structured data
                                import json
                                if text_content.startswith('[') or text_content.startswith('{'):
                                    parsed_data = json.loads(text_content)
                                    if isinstance(parsed_data, list):
                                        for item in parsed_data:
                                            if isinstance(item, dict):
                                                text = item.get("text", "").strip()
                                                score = item.get("score", 0.0)
                                                metadata = item.get("metadata", {})
                                                source_file = metadata.get("source", "unknown_file")
                                                collection = metadata.get("collection", collection_name or "Gen AI")

                                                # Format for aggregator agent: "text [Score: X.XXX] Source: file | Collection: collection"
                                                formatted_results.append(
                                                    f"{text} [Score: {score:.3f}] Source: {source_file} | Collection: {collection}"
                                                )
                                            else:
                                                formatted_results.append(f"• {str(item)}")
                                    else:
                                        # Single item result
                                        if isinstance(parsed_data, dict):
                                            text = parsed_data.get("text", "").strip()
                                            score = parsed_data.get("score", 0.0)
                                            metadata = parsed_data.get("metadata", {})
                                            source_file = metadata.get("source", "unknown_file")
                                            collection = metadata.get("collection", collection_name or "Gen AI")

                                            # Format for aggregator agent: "text [Score: X.XXX] Source: file | Collection: collection"
                                            formatted_results.append(
                                                f"{text} [Score: {score:.3f}] Source: {source_file} | Collection: {collection}"
                                            )
                                        else:
                                            formatted_results.append(f"• {str(parsed_data)}")
                                else:
                                    formatted_results.append(f"• {text_content}")
                            except Exception as parse_error:
                                logger.error(f"Error parsing result: {parse_error}")
                                formatted_results.append(f"• {text_content}")
                        else:
                            formatted_results.append(f"• {str(content_item)}")

                    final_result = "\n".join(formatted_results) if formatted_results else "No relevant information found in Qdrant."
                    return final_result

        # Run the query with overall timeout of 20 seconds
        result = await asyncio.wait_for(_query_with_timeout(), timeout=20.0)
        return result

    except asyncio.TimeoutError:
        logger.error(f"❌ Qdrant query timed out after 20 seconds")
        # Try direct API fallback on timeout
        try:
            logger.info("🔄 Attempting direct Qdrant API fallback due to timeout...")
            return await _direct_qdrant_search(query, collection_name)
        except Exception as fallback_error:
            logger.error(f"Direct API fallback also failed: {fallback_error}")
            return "❌ Qdrant search timed out. Please try again."
    except Exception as e:
        logger.error(f"Error calling Qdrant MCP tool: {e}")
        return f"❌ Error: {str(e)}"

async def test_qdrant_connection() -> bool:
    """Test if the Qdrant MCP server can be started and connected to."""
    try:
        # Get the server script path
        server_script = Path(__file__).parent / "qdrant.py"

        if not server_script.exists():
            logger.error(f"Qdrant server script not found: {server_script}")
            return False

        # Create stdio client session
        server_params = StdioServerParameters(
            command="python",
            args=[str(server_script)]
        )
        # Test connection
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                tools_result = await session.list_tools()
                tools = tools_result.tools if tools_result else []
                logger.info(f"✅ Qdrant MCP server accessible with {len(tools)} tools")
                return True

    except Exception as e:
        logger.error(f"❌ Cannot connect to Qdrant MCP server: {e}")
        return False

async def main():
    print("🔍 Qdrant MCP Agent - Vector Search (stdio transport)")
    print("=" * 60)
    print("📝 Note: No server URL needed - connects directly to qdrant1.py")
    print()

    # Test server connection first
    logger.info("Testing Qdrant MCP server connection...")
    if not await test_qdrant_connection():
        logger.error("❌ Qdrant MCP server is not accessible. Check your environment variables.")
        return

    # Test query
    print("🧪 Testing with sample query...")
    result = await query_qdrant_tool("artificial intelligence")
    print("📊 Query result:")
    print(result)

if __name__ == "__main__":
    asyncio.run(main())
