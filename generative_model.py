# Updated generative_model.py
#!/usr/bin/env python3
"""
Generative Model Component - Streaming LLM Response Generation
Handles final response generation with streaming support for Multi-Agent RAG
"""
import asyncio
import json
import logging
import requests
import time
from typing import Dict, List, Optional, Any, AsyncGenerator
from datetime import datetime
import os
from dotenv import load_dotenv
# from app.format_answer import enforce_structured_format

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GenerativeModel")

class GenerativeModel:
    """
    Generative model component that produces final responses with streaming support
    Integrates with multiple LLM providers
    """
    
    def __init__(self):
        # LLM endpoints
        self.endpoints = {
            "spaarxsenseaifabric": os.getenv("SPAARXSENSE_AI_FABRIC_ENDPOINT"),
            "groq": os.getenv("GROQ_ENDPOINT", "https://api.groq.com/openai/v1/chat/completions"),
            "google": os.getenv("GOOGLE_ENDPOINT", "https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent"),
            "openai": os.getenv("OPENAI_ENDPOINT", "https://api.openai.com/v1/chat/completions"),
            "anthropic": os.getenv("ANTHROPIC_ENDPOINT", "https://api.anthropic.com/v1/messages"),
            "huggingface": os.getenv("HUGGINGFACE_ENDPOINT", "https://api-inference.huggingface.co/models")
        }
        
        self.max_tokens = 8192
        logger.info("Generative Model initialized")
    
    def create_prompt(self, query: str, context: List[str], history: str = "",
                     mode: str = "agentic", host: str = "groq") -> str:
        """
        Create optimized prompt for the LLM with improved context handling

        Args:
            query: User query
            context: List of context strings from sub-agents
            history: Chat history
            mode: Query mode (rag, agentic, web)
            host: LLM host (for size optimization)

        Returns:
            Formatted prompt string
        """
        # Increased context limits for better responses
        if host == "groq":
            max_context_items = 5  # Increased from 3
            max_history_length = 1500  # Increased from 800
        else:
            max_context_items = 8  # Increased from 5
            max_history_length = 2500  # Increased from 1500

        # Limit context items only if absolutely necessary
        if context and len(context) > max_context_items:
            context = context[:max_context_items]
            logger.info(f"Limited context to {max_context_items} items for {host}")

        # Limit history length only if absolutely necessary
        if history and len(history) > max_history_length:
            history = history[-max_history_length:]
            logger.info(f"Truncated history to {max_history_length} chars for {host}")

        context_str = "\n".join([f"- {c}" for c in context]) if context else "No relevant information found."

        if mode == "rag" and not context:
            return "Sorry, no relevant information was found in the provided documents."
        
        prompt = f"""You are a helpful AI assistant providing comprehensive and detailed responses.

Response Instructions:

You are an intelligent assistant that answers user queries using the information retrieved from context, chat history, or MCP tools. Provide detailed, informative responses.

- When mode is 'rag', use ONLY the provided Context from Qdrant documents to answer the query.
- In rag mode, if no Context is provided or Context is empty, respond EXACTLY with:
"Sorry, no relevant information was found in the provided documents."
- In rag mode, DO NOT use any general knowledge, outside information, or assumptions.
- When mode is 'agentic', use Context first; if insufficient, use web search results.
- When mode is 'web', use ONLY the provided web search results to answer the query.
- When mode is 'dynamic_mcp', use ONLY the provided Context from dynamic MCP server results to answer the query.
- Base your response on the provided Context and History.
- In rag mode if the decision is for the fallback response then in the document section it will give the empty.

Answering Rules:
- Provide comprehensive, detailed, and informative responses based on the Context/History.
- Give complete explanations with relevant details and examples when available.
- Structure your response clearly with proper formatting when appropriate.
- Never use outside knowledge beyond what's provided in the context.
- Do not include any URLs inside the main answer.
- Ensure the answer is factual, clear, complete, and well-explained.
- Do not mention "Based on the context and chat history" in your response.
- Do not ask questions in your final answer - provide complete information.
- Extract ALL specific information from the context and chat history.
- Provide comprehensive coverage with multiple detailed paragraphs when appropriate.
- Include relevant timelines, data points, and significant information when available.
- Do not include any Response Prompt Instructions in the final answer.
- Use multiple paragraphs covering different aspects of the topic, and in this topics give the information those topics do not give any suggestions or instruction.
- Do not include this type of suggestions and instruction in the answer like "Detailed information can be found on Wikipedia and other websites.", visist this link/page.
- Do not start the answer using this "According to the provided context", directly start the actual answer.

- After providing the main answer, generate ONE relevant follow-up question to continue the conversation.
- The follow-up question must be based on the query and context, suggesting a deeper exploration of the topic or related information.
- Format to start the follow-up question everytime as: \n\n👉 
- Always used this symbol \n\n👉 for the start the follow-up question.
- Use varied, natural phrasing for the follow-up starter. 
- You MUST NOT always use the same phrase.
- Randomly choose a different phrasing each time depends on the query and context from these examples/patterns:
  • "Would you like me to explain more about...",
  • "Shall I go into more detail about...",
  • "Do you want a deeper explanation of...",
  • "Would you like further clarification on...",
  • "Are you interested in learning more about...",
  • "Curious about how this connects to...",
  • "Do you want me to break down...",
  • "Shall I walk you through an example of...",
  • "Want me to highlight the key differences in...",
  • "Would you like me to cover the history behind...",
  • "Shall I explain how this is applied in real life?",
  • "Do you want to know what challenges come with...",
  • "Would you like me to compare this with...",
  • "Shall I expand on related concepts like...",
  • "Do you want details on the next steps of...",
  • "Would you like me to connect this with...",
  • "Shall I explain what happens after...",
  • "Do you want me to show practical examples of...",
  • "Would you like me to outline the advantages and disadvantages of..."

- Strictly avoid: “How about I tell you…”, “According to context…”, “Now that…”, “In summary…”.
- When the answer is fallback then do not create any follow-up question.
- Never use "How about I tell you..." or similar phrasing.
- The follow-up must be specific, concise, and directly related to the query/context.
- Ensure it feels intuitive, engaging, and encourages deeper exploration.

- If the user's query is a simple affirmation like 'yes' or 'sure', interpret it as agreement to the previous follow-up question in the history, answer that specific question in detail, and then generate a new follow-up question.
- Ensure the follow-up is specific, concise, and directly related to the query/context.
- Do not generate a follow-up if the response is an error message or if no relevant context is provided.
- Do not include sensitive or inappropriate content in the response or follow-up.
- follow-up to feel intuitive and relevant, encouraging the user to dive deeper into the topic.

- The follow-up must be specific, creative, and directly derived from the query, context, and the content of your generated answer.
- The follow-up question must be based on the query and context, suggesting a deeper exploration of the topic or related information.
- Craft the follow-up to explore a related but distinct aspect of the topic, such as historical context, specific details, comparisons, practical applications, or examples.
- Ensure the follow-up is concise, engaging, and encourages deeper exploration of the topic.
- Ensure the follow-up is specific, concise, and directly related to the query/context.
- Do not generate a follow-up if the response is an error message or if no relevant context is provided.
- Do not include sensitive or inappropriate content in the response or follow-up.
- Use natural, varied, and conversational language for the follow-up, avoiding repetitive or formulaic phrasing.

You MUST provide detailed, specific, factual responses. DO NOT give generic responses that redirect users to websites.

STRICTLY FORBIDDEN RESPONSES:
- NEVER say "information can be found on Wikipedia/websites/platforms"
- NEVER say "readily available on various platforms"
- NEVER say "Wikipedia page provides" or "LinkedIn profile shows"
- NEVER say "comprehensive overview" or "in-depth look"
- NEVER use generic phrases like "renowned", "prominent figure", "well-known" without specific details
- NEVER give template-like responses that lack concrete facts
- NEVER redirect users to external sources instead of providing information
- NEVER mention checking external websites or platforms
- DO NOT add unnecessary "Introduction" sections - start directly with the main content
- Only use introductory text if it adds essential context, otherwise jump straight to the answer
- For factual queries, provide the facts immediately without preamble
- Do not give any Headings in the first of the answer start with the normal answer.

FORMATTING REQUIREMENTS:
- Structure your response clearly with proper formatting
- Use bullet points (•) for listing key facts or achievements
- Use numbered lists (1., 2., 3.) for sequential information or steps
- Use subheadings with **bold text** when covering different aspects
- Break long content into digestible paragraphs
- Use line breaks between different sections
- Format important dates, numbers, and statistics clearly
- Make the response easy to read and well-organized

- Structure information logically with proper formatting and organization
- Use bullet points (•) and numbered lists (1., 2., 3.) to organize key information
- Add **bold subheadings** when covering different aspects of a topic
- Break content into digestible sections with line breaks between topics
- NEVER start with "Introduction to [topic]" or "**Introduction to [topic]**"
- Provide complete, substantial, fact-dense responses that are well-structured and easy to read querys answers

**Context:**
{context_str}

**Chat History:**
{history}

**User Query:**
{query}

**Response:**"""
        
        return prompt
    
    async def generate_response(self, query: str, context: List[str], host: str, 
                              model: str, api_key: str, history: str = "", 
                              mode: str = "agentic", stream: bool = False) -> dict:
        """
        Generate non-streaming response

        Args:
            query: User query
            context: Context from sub-agents
            host: LLM provider
            model: Model name
            api_key: API key
            history: Chat history
            mode: Query mode
            stream: Whether to stream (ignored in this method, kept for compatibility)

        Returns:
            Dict containing answer and follow-up question
        """
        prompt = self.create_prompt(query, context, history, mode, host)

        if mode == "rag" and not context:
            return {"answer": "Sorry, no relevant information was found in the provided documents.",
                    "follow_up": None}

        try:
            response = await self._call_llm(host, model, api_key, prompt, stream=False)
            response = self.validate_response(response, mode, context)

            # If the model didn't generate a follow-up, append one manually
            if "👉" not in response:
                followup_patterns = [
                    "Would you like me to explain more about this?",
                    "Shall I go into more detail about this?",
                    "Do you want a deeper explanation of this?",
                    "Would you like further clarification on this?",
                    "Are you interested in learning more about this?",
                    "Curious about how this connects to other aspects?",
                    "Do you want me to break this down further?",
                    "Shall I walk you through an example?",
                    "Want me to highlight the key differences?",
                    "Would you like me to cover the history behind this?",
                    "Shall I explain how this applies in real life?",
                    "Do you want to know the challenges that come with this?",
                    "Would you like me to compare this with something else?",
                    "Shall I expand on related concepts?",
                    "Do you want details on the next steps?",
                    "Would you like me to connect this with another field?",
                    "Shall I explain what happens after this?",
                    "Do you want me to show practical examples?",
                    "Would you like me to outline the advantages and disadvantages?"
                ]
                import random
                followup = random.choice(followup_patterns)
                response = response.strip() + f"\n\n👉 {followup}"

            return {"answer": response,"follow_up": None}

        except Exception as e:
            return {"answer": f"Error generating response: {str(e)}"}

    async def generate_streaming_response(self, query: str, context: List[str], 
                                        host: str, model: str, api_key: str, 
                                        history: str = "", mode: str = "agentic") -> AsyncGenerator[str, None]:
        """
        Generate streaming response
        
        Args:
            query: User query
            context: Context from sub-agents
            host: LLM provider
            model: Model name
            api_key: API key
            history: Chat history
            mode: Query mode
            
        Yields:
            Response chunks
        """
        prompt = self.create_prompt(query, context, history, mode)
        
        if mode == "rag" and not context:
            yield "Sorry, no relevant information was found in the provided documents."
            return
        
        try:
            # For demonstration, we'll simulate streaming by chunking the response
            full_response = await self._call_llm(host, model, api_key, prompt, stream=False)
            full_response = self.validate_response(full_response, mode, context)
            
            # Simulate streaming by yielding words
            words = full_response.split()
            for i, word in enumerate(words):
                if i == 0:
                    yield word
                else:
                    yield f" {word}"
                await asyncio.sleep(0.05)  # Small delay to simulate streaming
                
        except Exception as e:
            logger.error(f"Error in streaming response: {e}")
            yield f"Error generating response: {str(e)}"
    
    async def _call_llm(self, host: str, model: str, api_key: str, prompt: str, 
                       stream: bool = False) -> str:
        """
        Call the specified LLM provider
        
        Args:
            host: LLM provider
            model: Model name
            api_key: API key
            prompt: Input prompt
            stream: Whether to stream
            
        Returns:
            LLM response
        """
        headers = {"Content-Type": "application/json"}
        
        if host == "spaarxsenseaifabric":
            return await self._call_spaarxsense(model, api_key, prompt, headers)
        elif host == "groq":
            return await self._call_groq(model, api_key, prompt, headers)
        elif host == "google":
            return await self._call_google(model, api_key, prompt, headers)
        elif host == "openai":
            return await self._call_openai(model, api_key, prompt, headers)
        elif host == "anthropic":
            return await self._call_anthropic(model, api_key, prompt, headers)
        elif host in ["meta", "microsoft"]:
            return await self._call_huggingface(host, model, api_key, prompt, headers)
        else:
            raise ValueError(f"Unsupported host: {host}")
    
    async def _call_spaarxsense(self, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call SpaarxSense AI Fabric"""
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False
        }
        headers["Authorization"] = f"Bearer {api_key}"
        
        response = requests.post(self.endpoints["spaarxsenseaifabric"], 
                               json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get("response", "No response generated.")
    
    async def _call_groq(self, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call Groq API with improved context handling"""
        # Increased limit for better responses
        max_prompt_length = 12000  # Increased from 6000 for more comprehensive responses

        if len(prompt) > max_prompt_length:
            logger.warning(f"Groq: Prompt too long ({len(prompt)} chars), truncating to {max_prompt_length}")
            # Smarter truncation that preserves more context
            lines = prompt.split('\n')

            # Find key sections
            query_start = -1
            context_start = -1
            history_start = -1

            for i, line in enumerate(lines):
                if "**User Query:**" in line:
                    query_start = i
                elif "**Context:**" in line:
                    context_start = i
                elif "**Chat History:**" in line:
                    history_start = i

            # Preserve instructions and query, truncate context/history intelligently
            if query_start > 0:
                instructions = '\n'.join(lines[:context_start if context_start > 0 else query_start])
                query_section = '\n'.join(lines[query_start:]) if query_start > 0 else ""

                # Calculate available space for context
                essential_length = len(instructions) + len(query_section)
                available_for_context = max_prompt_length - essential_length - 200  # Buffer

                if context_start > 0 and available_for_context > 500:
                    context_section = '\n'.join(lines[context_start:query_start if query_start > context_start else len(lines)])
                    if len(context_section) > available_for_context:
                        # Truncate context but keep the most relevant parts
                        context_section = context_section[:available_for_context] + "\n[Context truncated for length]"
                    prompt = instructions + '\n' + context_section + '\n' + query_section
                else:
                    prompt = instructions + '\n' + query_section
            else:
                # Fallback: simple truncation
                prompt = prompt[:max_prompt_length]

            logger.info(f"Groq: Truncated prompt to {len(prompt)} characters")

        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "stream": False,
            "max_tokens": min(self.max_tokens, 6144)  # Increased for more detailed responses
        }
        headers["Authorization"] = f"Bearer {api_key}"

        # Retry logic for rate limiting
        max_retries = 2
        retry_delay = 1  # seconds

        for attempt in range(max_retries + 1):
            try:
                response = requests.post(self.endpoints["groq"],
                                       json=payload, headers=headers, timeout=30)
                response.raise_for_status()
                data = response.json()
                return data.get("choices", [{}])[0].get("message", {}).get("content", "No response generated.")
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 413:
                    logger.error("Groq: Payload still too large after truncation, using fallback response")
                    return "I apologize, but the context is too large for processing. Please try with a shorter query or fewer documents."
                elif e.response.status_code == 429:
                    if attempt < max_retries:
                        logger.warning(f"Groq: Rate limit exceeded, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries + 1})")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                        continue
                    else:
                        logger.error("Groq: Rate limit exceeded after all retries, using fallback response")
                        return "I apologize, but the service is currently experiencing high demand. Please try again in a few moments, or consider using a different model."
                else:
                    logger.error(f"Groq API error {e.response.status_code}: {e}")
                    return f"I apologize, but there was an error processing your request. Please try again later."
            except Exception as e:
                logger.error(f"Groq: Unexpected error: {e}")
                return f"I apologize, but there was an unexpected error. Please try again later."
    
    async def _call_google(self, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call Google Gemini API"""
        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {"maxOutputTokens": self.max_tokens}
        }
        headers["x-goog-api-key"] = api_key
        
        response = requests.post(self.endpoints["google"], 
                               json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get("candidates", [{}])[0].get("content", {}).get("parts", [{}])[0].get("text", "No response generated.")
    
    async def _call_openai(self, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call OpenAI API"""
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "stream": False,
            "max_tokens": self.max_tokens
        }
        headers["Authorization"] = f"Bearer {api_key}"
        
        response = requests.post(self.endpoints["openai"], 
                               json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get("choices", [{}])[0].get("message", {}).get("content", "No response generated.")
    
    async def _call_anthropic(self, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call Anthropic Claude API"""
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": self.max_tokens
        }
        headers.update({
            "x-api-key": api_key,
            "anthropic-version": "2023-06-01"
        })
        
        response = requests.post(self.endpoints["anthropic"], 
                               json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get("content", [{}])[0].get("text", "No response generated.")
    
    async def _call_huggingface(self, host: str, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call HuggingFace API for Meta/Microsoft models"""
        payload = {"inputs": prompt, "parameters": {"max_new_tokens": self.max_tokens}}
        headers["Authorization"] = f"Bearer {api_key}"
        
        response = requests.post(f"{self.endpoints['huggingface']}/{model}", 
                               json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            return data[0].get("generated_text", "No response generated.")
        return "No response generated."
    
    def validate_response(self, response: str, mode: str, context: List[str]) -> str:
        """
        Validate and clean the generated response

        Args:
            response: Generated response
            mode: Query mode
            context: Original context

        Returns:
            Validated response
        """
        if not response or not response.strip():
            if mode == "rag":
                return "Sorry, no relevant information was found in the provided documents."
            else:
                return "I apologize, but I couldn't generate a proper response. Please try again."

        # Clean up common issues
        response = response.strip()

        # Detect and reject generic responses (universal patterns)
        generic_indicators = [
            "information can be found on",
            "details can be found on",
            "detailed information about.*can be found",
            "check wikipedia",
            "visit.*website",
            "sources provide comprehensive overviews",
            "online platforms, such as",
            "profile on.*offers",
            "wikipedia page provides",
            "overall,.*is a prominent figure",
            "overall,.*is a well-known",
            "overall,.*is a renowned",
            "is a renowned.*who is",
            "is a well-known.*in the",
            "prominent figure in the",
            "can be found on various online platforms",
            "these sources provide comprehensive",
            "for more information, visit",
            "you can find more details on",
            "readily available on various platforms",
            "available on various platforms",
            "his wikipedia page provides",
            "her wikipedia page provides",
            "wikipedia and linkedin",
            "including wikipedia and",
            "comprehensive overview of",
            "offering a comprehensive overview",
            "provides an in-depth look",
            "most influential.*in the world"
        ]

        response_lower = response.lower()

        # First, try to clean out generic phrases while keeping good content
        cleaned_response = response
        generic_phrases_to_remove = [
            r".*readily available on various platforms.*",
            r".*available on various platforms.*",
            r".*wikipedia page provides.*",
            r".*including wikipedia and.*",
            r".*comprehensive overview of.*",
            r".*offering a comprehensive overview.*",
            r".*provides an in-depth look.*",
            r".*can be found on.*",
            r".*details can be found.*",
            r".*information.*available on.*",
            r".*sources provide comprehensive.*",
            r".*according to various sources.*?(?=\*\*|$)",
            r"^\*\*Introduction.*?\*\*.*?(?=\*\*|$)",  # Remove Introduction sections
            r"^Introduction to.*?(?=\*\*|$)",  # Remove "Introduction to" paragraphs
            r".*industry boasts.*?(?=\*\*|$)",  # Remove generic industry descriptions
            r"^\*\*Introduction to [^*]+\*\*\s*",  # Remove "**Introduction to X**" at start
            r"^Introduction to [^*\n]+\n*",  # Remove "Introduction to X" at start
            r"^\*\*Introduction[^*]*\*\*\s*",  # Remove any "**Introduction...**" at start
            r"^Introduction[^*\n]*\n*",  # Remove any "Introduction..." at start
            r"^\*\*[^*]*Introduction[^*]*\*\*\s*",  # Remove any title with "Introduction"
            r"^[^*\n]*Introduction[^*\n]*\n*"  # Remove any line starting with Introduction
        ]


        import re
        for pattern in generic_phrases_to_remove:
            cleaned_response = re.sub(pattern, "", cleaned_response, flags=re.IGNORECASE | re.DOTALL)

        # Clean up extra whitespace and incomplete sentences
        cleaned_response = re.sub(r'\s+', ' ', cleaned_response).strip()
        cleaned_response = re.sub(r'\.\s*\.\s*', '. ', cleaned_response)  # Remove double periods

        # If cleaning removed too much content, check for generic indicators
        if len(cleaned_response) < len(response) * 0.3:  # If we removed more than 70% of content
            for indicator in generic_indicators:
                if indicator in response_lower:
                    logger.warning(f"Generic response detected with indicator: {indicator}")
                    # If we have good context, try to extract facts from it
                    if context and any(len(ctx.strip()) > 50 for ctx in context):
                        # Try to create a better response from the context
                        context_facts = []
                        for ctx in context:
                            if len(ctx.strip()) > 30:  # Only use substantial context
                                context_facts.append(ctx.strip())

                        if context_facts:
                            return f"{' '.join(context_facts[:3])}"
                        else:
                            return "I need to provide more specific information. Let me give you detailed facts instead of generic descriptions."
                    else:
                        return "I apologize, but I need more specific information to provide a detailed response about this topic."

        # If we have a good cleaned response, use it
        if len(cleaned_response.strip()) > 50:
            response = cleaned_response

        # Enhanced RAG mode validation - prevent general knowledge responses
        if mode == "rag":
            # If no context was provided, return fallback message
            if not context or not any(ctx.strip() for ctx in context):
                return "Sorry, no relevant information was found in the provided documents."

            # Check if response seems to be using general knowledge
            general_knowledge_indicators = [
                "based on my knowledge",
                "generally speaking",
                "in general",
                "typically",
                "usually",
                "commonly",
                "it is known that",
                "as far as I know"
            ]

            response_lower = response.lower()
            if any(indicator in response_lower for indicator in general_knowledge_indicators):
                logger.warning("RAG mode: Detected potential general knowledge response, returning fallback")
                return "Sorry, no relevant information was found in the provided documents."

        # Web mode has no fallback - just return the response as-is

        # Remove any system prompts that might have leaked through
        if response.startswith("You are a helpful"):
            lines = response.split('\n')
            for i, line in enumerate(lines):
                if line.strip() and not line.startswith("You are") and not line.startswith("**"):
                    response = '\n'.join(lines[i:])
                    break

        return response

# Global generative model 
generative_model = None

def get_generative_model() -> GenerativeModel:
    """Get or create generative model instance"""
    global generative_model
    if generative_model is None:
        generative_model = GenerativeModel()
    return generative_model

async def test_generative_model():
    """Test the generative model with comprehensive scenarios"""
    print("🤖 GENERATIVE MODEL TEST")
    print("=" * 60)

    try:
        # Initialize generative model
        model = get_generative_model()
        print("✅ Generative model initialized")

        # Test contexts and queries
        test_scenarios = [
            {
                "query": "What is machine learning?",
                "context": ["Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed."],
                "mode": "rag",
                "description": "RAG mode with good context"
            },
            {
                "query": "What is quantum computing?",
                "context": [],
                "mode": "rag",
                "description": "RAG mode with no context (should return fallback)"
            },
            {
                "query": "Latest developments in AI",
                "context": ["Recent AI developments include large language models, computer vision advances, and robotics improvements."],
                "mode": "web",
                "description": "Web mode with context"
            },
            {
                "query": "How does neural network work?",
                "context": ["Neural networks are computing systems inspired by biological neural networks.", "They consist of layers of interconnected nodes that process information."],
                "mode": "agentic",
                "description": "Agentic mode with multiple context items"
            }
        ]

        print(f"\n🧪 Testing {len(test_scenarios)} different scenarios...")

        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n🔍 Test {i}: {scenario['description']}")
            print(f"Query: '{scenario['query']}'")
            print(f"Mode: {scenario['mode']}")
            print(f"Context items: {len(scenario['context'])}")
            print("-" * 40)

            try:
                # Test 1: Prompt creation
                prompt = model.create_prompt(
                    query=scenario['query'],
                    context=scenario['context'],
                    history="User: Hello\nAssistant: Hi! How can I help you?",
                    mode=scenario['mode']
                )
                print(f"✅ Prompt created: {len(prompt)} characters")

                # Show prompt preview
                prompt_preview = prompt[:200] + "..." if len(prompt) > 200 else prompt
                print(f"   Preview: {prompt_preview}")

                # Test 2: Response validation
                test_responses = [
                    "This is a good response about machine learning.",
                    "",  # Empty response
                    "You are a helpful assistant. Machine learning is...",  # Response with system prompt
                    "Sorry, no relevant information was found."  # Fallback response
                ]

                print(f"✅ Response validation tests:")
                for j, test_response in enumerate(test_responses):
                    validated = model.validate_response(test_response, scenario['mode'], scenario['context'])
                    status = "✅" if validated != test_response else "➡️"
                    print(f"   {status} Test {j+1}: {len(validated)} chars")

                # Test 3: Streaming simulation (without actual API call)
                print(f"✅ Streaming simulation:")
                print("   Response: ", end="")

                # Simulate streaming with a mock response
                mock_response = f"This is a test response for the query about {scenario['query'].split()[-1] if scenario['query'].split() else 'the topic'}. The response demonstrates the streaming functionality."

                words = mock_response.split()
                for k, word in enumerate(words[:10]):  # Limit to first 10 words for testing
                    if k == 0:
                        print(word, end="", flush=True)
                    else:
                        print(f" {word}", end="", flush=True)
                    await asyncio.sleep(0.01)  # Small delay to simulate streaming

                if len(words) > 10:
                    print("...", end="")
                print()  # New line

            except Exception as e:
                print(f"❌ Test failed: {str(e)}")

        # Test 4: Different LLM provider configurations
        print(f"\n🔧 Test: LLM Provider Configurations")
        print("-" * 30)

        providers = [
            {"host": "groq", "model": "llama3-8b-8192"},
            {"host": "openai", "model": "gpt-3.5-turbo"},
            {"host": "google", "model": "gemini-pro"},
            {"host": "anthropic", "model": "claude-3-sonnet"},
            {"host": "spaarxsenseaifabric", "model": "deepseek-r1:1.5b"}
        ]

        for provider in providers:
            try:
                # Test prompt creation for each provider
                test_prompt = model.create_prompt(
                    "Test query",
                    ["Test context"],
                    "",
                    "agentic"
                )
                print(f"✅ {provider['host']}: Prompt ready for {provider['model']}")
            except Exception as e:
                print(f"⚠️ {provider['host']}: Configuration issue - {str(e)}")

        # Test 5: Different modes
        print(f"\n🎯 Test: Mode-Specific Behavior")
        print("-" * 30)

        test_query = "What is artificial intelligence?"
        modes = ["rag", "agentic", "web"]

        for mode in modes:
            # Test with context
            prompt_with_context = model.create_prompt(test_query, ["AI is a technology"], "", mode)
            # Test without context
            prompt_without_context = model.create_prompt(test_query, [], "", mode)

            print(f"✅ {mode.upper()} mode:")
            print(f"   With context: {len(prompt_with_context)} chars")
            print(f"   Without context: {len(prompt_without_context)} chars")

            # Special handling for RAG mode without context
            if mode == "rag":
                no_context_response = model.create_prompt(test_query, [], "", mode)
                if "Sorry, no relevant information" in no_context_response:
                    print(f"   ✅ RAG fallback message working correctly")

        print("\n" + "=" * 60)
        print("🎉 Generative Model testing completed!")
        print("📊 Summary:")
        print(f"   - Tested prompt creation for different modes and contexts")
        print(f"   - Validated response cleaning and formatting")
        print(f"   - Simulated streaming functionality")
        print(f"   - Verified provider configurations")
        print("📝 Note: Actual LLM API calls require valid API keys and network access")

    except Exception as e:
        print(f"❌ Generative model test failed: {str(e)}")
        print("💡 Make sure dependencies are installed:")
        print("   pip install requests python-dotenv")

if __name__ == "__main__":
    print("🚀 Starting Generative Model Test...")
    asyncio.run(test_generative_model())